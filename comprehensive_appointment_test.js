/**
 * Comprehensive Appointment Workflow Test
 * Tests the complete appointment workflow from creation to receipt generation
 * for 4 pets (2 registered, 2 walk-ins) at Adolf Clinic
 */

import mongoose from 'mongoose';
import Client from './models/client.model.js';
import Pet from './models/pet.model.js';
import Appointment from './models/appointment.model.js';
import AppointmentType from './models/appointmentType.model.js';
import Service from './models/service.model.js';
import Species from './models/species.model.js';
import Breed from './models/breed.model.js';
import Staff from './models/staff.model.js';
import AISuggestion from './models/aiSuggestion.model.js';
import bcrypt from 'bcrypt';

// MongoDB connection
const MONGODB_URI = "mongodb+srv://kabiujm:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";

// Test configuration
const ADOLF_CLINIC_ID = 1019;
const TEST_STAFF_ID = 1020;

// Test data
const testClients = [
  // Registered clients
  {
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phoneNumber: '+254701234567',
    address: '123 Nairobi Street, Nairobi',
    isWalkIn: false
  },
  {
    firstName: 'Sarah',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phoneNumber: '+254701234568',
    address: '456 Mombasa Road, Nairobi',
    isWalkIn: false
  },
  // Walk-in clients
  {
    firstName: 'Michael',
    lastName: 'Brown',
    email: '<EMAIL>',
    phoneNumber: '+254701234569',
    address: '789 Kiambu Road, Nairobi',
    isWalkIn: true
  },
  {
    firstName: 'Emma',
    lastName: 'Wilson',
    email: '<EMAIL>',
    phoneNumber: '+254701234570',
    address: '321 Thika Road, Nairobi',
    isWalkIn: true
  }
];

const testPets = [
  // John Smith's pet (registered)
  {
    name: 'Buddy',
    species: 'Dog',
    breed: 'German Shepherd',
    gender: 'male',
    color: 'Golden',
    weight: 30.5,
    microchipId: 'MC001234567890',
    appointmentTypes: ['Vaccination', 'Consultation']
  },
  // Sarah Johnson's pet (registered)
  {
    name: 'Luna',
    species: 'Cat',
    breed: 'Puss cat',
    gender: 'female',
    color: 'White',
    weight: 4.2,
    microchipId: 'MC001234567891',
    appointmentTypes: ['Laboratory', 'Surgery']
  },
  // Michael Brown's pet (walk-in)
  {
    name: 'Max',
    species: 'Dog',
    breed: 'German Shepherd',
    gender: 'male',
    color: 'Brown',
    weight: 35.0,
    microchipId: 'MC001234567892',
    appointmentTypes: ['Emergency', 'Medication']
  },
  // Emma Wilson's pet (walk-in)
  {
    name: 'Bella',
    species: 'Dog',
    breed: 'German Shepherd',
    gender: 'female',
    color: 'Black',
    weight: 28.0,
    microchipId: 'MC001234567893',
    appointmentTypes: ['Grooming', 'Dental']
  }
];

class AppointmentWorkflowTester {
  constructor() {
    this.createdClients = [];
    this.createdPets = [];
    this.createdAppointments = [];
    this.species = {};
    this.breeds = {};
    this.appointmentTypes = {};
    this.services = {};
  }

  async connect() {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  }

  async loadReferenceData() {
    console.log('📚 Loading reference data...');

    // Load species
    const speciesData = await Species.find({}).lean();
    this.species = speciesData.reduce((map, s) => {
      map[s.speciesName.toLowerCase()] = s;
      return map;
    }, {});

    // Load breeds
    const breedsData = await Breed.find({}).lean();
    this.breeds = breedsData.reduce((map, b) => {
      map[b.breedName.toLowerCase()] = b;
      return map;
    }, {});

    // Load appointment types
    const appointmentTypesData = await AppointmentType.find({}).lean();
    this.appointmentTypes = appointmentTypesData.reduce((map, at) => {
      map[at.name] = at;
      return map;
    }, {});

    // Load services
    const servicesData = await Service.find({ clinicId: ADOLF_CLINIC_ID }).lean();
    this.services = servicesData.reduce((map, s) => {
      if (!map[s.category]) map[s.category] = [];
      map[s.category].push(s);
      return map;
    }, {});

    console.log(`✅ Loaded ${Object.keys(this.species).length} species, ${Object.keys(this.breeds).length} breeds`);
    console.log(`✅ Loaded ${Object.keys(this.appointmentTypes).length} appointment types`);
    console.log(`✅ Loaded services for ${Object.keys(this.services).length} categories`);
  }

  async createTestClients() {
    console.log('👥 Creating test clients...');

    for (const clientData of testClients) {
      try {
        // Check if client already exists
        const existingClient = await Client.findOne({ email: clientData.email });
        if (existingClient) {
          console.log(`⚠️  Client ${clientData.firstName} ${clientData.lastName} already exists`);
          this.createdClients.push(existingClient);
          continue;
        }

        const hashedPassword = await bcrypt.hash('password123', 10);
        const client = new Client({
          ...clientData,
          password: hashedPassword,
          homeLocation: [0, 0],
          clientStatus: 1
        });

        const savedClient = await client.save();
        this.createdClients.push(savedClient);
        console.log(`✅ Created ${clientData.isWalkIn ? 'walk-in' : 'registered'} client: ${savedClient.firstName} ${savedClient.lastName} (ID: ${savedClient.clientId})`);
      } catch (error) {
        console.error(`❌ Error creating client ${clientData.firstName}:`, error.message);
      }
    }
  }

  async createTestPets() {
    console.log('🐕 Creating test pets...');

    for (let i = 0; i < testPets.length; i++) {
      const petData = testPets[i];
      const client = this.createdClients[i];

      if (!client) {
        console.error(`❌ No client found for pet ${petData.name}`);
        continue;
      }

      try {
        // Check if pet already exists
        const existingPet = await Pet.findOne({
          name: petData.name,
          ownerId: client.clientId
        });

        if (existingPet) {
          console.log(`⚠️  Pet ${petData.name} already exists for ${client.firstName}`);
          this.createdPets.push(existingPet);
          continue;
        }

        const speciesData = this.species[petData.species];
        const breedData = this.breeds[petData.breed];

        console.log(`Looking for species: ${petData.species}, found:`, speciesData);
        console.log(`Looking for breed: ${petData.breed}, found:`, breedData);
        console.log('Available species:', Object.keys(this.species));
        console.log('Available breeds:', Object.keys(this.breeds));

        if (!speciesData || !breedData) {
          console.error(`❌ Species or breed not found for ${petData.name}`);
          continue;
        }

        const pet = new Pet({
          petName: petData.name,
          name: petData.name,
          speciesId: speciesData.speciesId,
          breedId: breedData.breedId,
          gender: petData.gender,
          color: petData.color,
          weight: petData.weight,
          microchipId: petData.microchipId,
          dateOfBirth: new Date(2020, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
          lifeStatus: 'alive',
          owner: client.clientId,
          ownerId: client.clientId,
          petStatus: 1,
          registrationInfo: {
            registeredAt: ADOLF_CLINIC_ID,
            registeredBy: TEST_STAFF_ID,
            registrationDate: new Date()
          }
        });

        const savedPet = await pet.save();
        this.createdPets.push(savedPet);
        console.log(`✅ Created pet: ${savedPet.name} (${petData.species}) for ${client.firstName} ${client.lastName}`);
      } catch (error) {
        console.error(`❌ Error creating pet ${petData.name}:`, error.message);
      }
    }
  }

  async createTestAppointments() {
    console.log('📅 Creating test appointments...');

    for (let i = 0; i < this.createdPets.length; i++) {
      const pet = this.createdPets[i];
      const client = this.createdClients[i];
      const petData = testPets[i];

      if (!pet || !client) {
        console.error(`❌ Missing pet or client data for appointment ${i + 1}`);
        continue;
      }

      try {
        // Get appointment types for this pet
        const appointmentTypesForPet = petData.appointmentTypes.map(typeName =>
          this.appointmentTypes[typeName]
        ).filter(Boolean);

        if (appointmentTypesForPet.length === 0) {
          console.error(`❌ No valid appointment types found for ${pet.name}`);
          continue;
        }

        // Calculate total duration and price
        const totalDuration = appointmentTypesForPet.reduce((sum, at) => sum + at.defaultDuration, 0);
        const totalPrice = appointmentTypesForPet.reduce((sum, at) => sum + at.price, 0);

        // Create appointment date (tomorrow at random time)
        const appointmentDate = new Date();
        appointmentDate.setDate(appointmentDate.getDate() + 1);
        appointmentDate.setHours(9 + Math.floor(Math.random() * 8), 0, 0, 0); // 9 AM to 5 PM

        const appointment = new Appointment({
          clientId: client.clientId,
          petId: pet.petId,
          clinicId: ADOLF_CLINIC_ID,
          staffId: TEST_STAFF_ID,
          dateTime: appointmentDate,
          duration: totalDuration,
          appointmentTypes: appointmentTypesForPet.map(at => ({
            appointmentTypeId: at.appointmentTypeId,
            name: at.name,
            duration: at.defaultDuration,
            price: at.price
          })),
          status: 'scheduled',
          totalAmount: totalPrice,
          currency: 'KES',
          notes: `Test appointment for ${pet.name} - ${petData.appointmentTypes.join(', ')}`,
          // Additional fields for workflow
          clientName: `${client.firstName} ${client.lastName}`,
          petName: pet.name,
          petSpecies: petData.species,
          petBreed: petData.breed
        });

        const savedAppointment = await appointment.save();
        this.createdAppointments.push(savedAppointment);
        console.log(`✅ Created appointment: ${savedAppointment.appointmentId} for ${pet.name} (${petData.appointmentTypes.join(', ')})`);
      } catch (error) {
        console.error(`❌ Error creating appointment for ${pet.name}:`, error.message);
      }
    }
  }

  async simulateWorkflow() {
    console.log('🔄 Simulating appointment workflow...');

    for (const appointment of this.createdAppointments) {
      console.log(`\n📋 Processing appointment ${appointment.appointmentId} for ${appointment.petName}`);

      try {
        // Step 1: Assign categories to staff
        await this.assignCategoriesToStaff(appointment);

        // Step 2: Add services to appointment
        await this.addServicesToAppointment(appointment);

        // Step 3: Generate AI suggestions
        await this.generateAISuggestions(appointment);

        // Step 4: Complete tasks
        await this.completeTasks(appointment);

        // Step 5: Generate summary and receipt
        await this.generateSummaryAndReceipt(appointment);

        console.log(`✅ Completed workflow for appointment ${appointment.appointmentId}`);
      } catch (error) {
        console.error(`❌ Error in workflow for appointment ${appointment.appointmentId}:`, error.message);
      }
    }
  }

  async assignCategoriesToStaff(appointment) {
    console.log(`  📝 Assigning categories to staff...`);

    // Map appointment types to categories
    const appointmentTypeToCategory = {
      'Vaccination': 'vaccination',
      'Consultation': 'consultation',
      'Laboratory': 'laboratory',
      'Surgery': 'surgery',
      'Emergency': 'emergency',
      'Medication': 'medication',
      'Grooming': 'grooming',
      'Dental': 'dental'
    };

    const selectedCategories = appointment.appointmentTypes.map(at => ({
      category: appointmentTypeToCategory[at.name] || 'consultation',
      assignedStaff: TEST_STAFF_ID,
      estimatedDuration: at.duration || 30,
      priority: at.name === 'Emergency' ? 'urgent' : 'normal',
      isCompleted: false
    }));

    // Update appointment with category assignments
    await Appointment.findOneAndUpdate(
      { appointmentId: appointment.appointmentId },
      {
        selectedCategories,
        vetInCharge: TEST_STAFF_ID,
        completionStatus: 'in_progress',
        status: 'in_progress'
      }
    );

    console.log(`    ✅ Assigned ${selectedCategories.length} categories to staff`);
  }

  async addServicesToAppointment(appointment) {
    console.log(`  🛠️  Adding services to appointment...`);

    const appointmentServices = [];

    // Add services based on categories
    for (const categoryAssignment of appointment.selectedCategories || []) {
      const categoryServices = this.services[categoryAssignment.category] || [];

      if (categoryServices.length > 0) {
        // Add 1-2 services per category
        const servicesToAdd = categoryServices.slice(0, Math.min(2, categoryServices.length));

        for (const service of servicesToAdd) {
          appointmentServices.push({
            serviceId: service.serviceId,
            serviceName: service.serviceName,
            price: service.defaultPrice,
            currency: service.currency,
            notes: `Service performed for ${categoryAssignment.category} category`,
            assignedStaffId: TEST_STAFF_ID,
            assignedStaffName: 'Test Staff'
          });
        }
      }
    }

    // Update appointment with services
    await Appointment.findOneAndUpdate(
      { appointmentId: appointment.appointmentId },
      { services: appointmentServices }
    );

    console.log(`    ✅ Added ${appointmentServices.length} services to appointment`);
  }

  async generateAISuggestions(appointment) {
    console.log(`  🤖 Generating AI suggestions...`);

    const suggestions = [
      {
        appointmentId: appointment.appointmentId,
        petId: appointment.petId,
        category: 'diagnosis',
        suggestionType: 'diagnostic_test',
        title: 'Recommended Diagnostic Tests',
        description: 'Based on the examination findings, the following tests are recommended.',
        confidenceScore: 85,
        priority: 'medium',
        diagnosticTests: [
          {
            testName: 'Complete Blood Count',
            reason: 'To assess overall health status',
            urgency: 'routine',
            estimatedCost: 150
          }
        ],
        aiModel: {
          provider: 'openai',
          model: 'gpt-4',
          version: '1.0'
        },
        context: {
          symptoms: ['routine_checkup'],
          vitalSigns: {
            temperature: 38.5,
            heartRate: 120,
            weight: appointment.petSpecies === 'Dog' ? 30 : 5
          }
        },
        generatedBy: TEST_STAFF_ID,
        status: 'pending'
      }
    ];

    // Save AI suggestions
    await AISuggestion.insertMany(suggestions);
    console.log(`    ✅ Generated ${suggestions.length} AI suggestions`);
  }

  async completeTasks(appointment) {
    console.log(`  ✅ Completing tasks...`);

    // Mark all categories as completed
    const updatedCategories = (appointment.selectedCategories || []).map(cat => ({
      ...cat,
      isCompleted: true,
      completedAt: new Date(),
      completedBy: TEST_STAFF_ID,
      signature: `Completed by Staff ${TEST_STAFF_ID}`,
      actualDuration: cat.estimatedDuration + Math.floor(Math.random() * 10) // Add some variance
    }));

    await Appointment.findOneAndUpdate(
      { appointmentId: appointment.appointmentId },
      {
        selectedCategories: updatedCategories,
        completionStatus: 'completed',
        status: 'completed'
      }
    );

    console.log(`    ✅ Completed ${updatedCategories.length} tasks`);
  }

  async generateSummaryAndReceipt(appointment) {
    console.log(`  📄 Generating summary and receipt...`);

    const summaryContent = `
Appointment Summary for ${appointment.petName}
Date: ${new Date(appointment.dateTime).toLocaleDateString()}
Client: ${appointment.clientName}
Pet: ${appointment.petName} (${appointment.petSpecies})

Services Performed:
${(appointment.services || []).map(s => `- ${s.serviceName}: KES ${s.price}`).join('\n')}

Total Amount: KES ${appointment.totalAmount}
Status: Completed

All tasks completed successfully.
    `.trim();

    // Update appointment with AI summary
    await Appointment.findOneAndUpdate(
      { appointmentId: appointment.appointmentId },
      {
        aiSummary: {
          content: summaryContent,
          generatedAt: new Date(),
          generatedBy: TEST_STAFF_ID,
          isApproved: true,
          approvedBy: TEST_STAFF_ID,
          approvedAt: new Date()
        }
      }
    );

    console.log(`    ✅ Generated appointment summary and receipt`);
  }

  async printTestResults() {
    console.log('\n📊 TEST RESULTS SUMMARY');
    console.log('========================');
    console.log(`✅ Created ${this.createdClients.length} clients (${this.createdClients.filter(c => !c.isWalkIn).length} registered, ${this.createdClients.filter(c => c.isWalkIn).length} walk-ins)`);
    console.log(`✅ Created ${this.createdPets.length} pets`);
    console.log(`✅ Created ${this.createdAppointments.length} appointments`);

    console.log('\n📋 APPOINTMENT DETAILS:');
    for (const appointment of this.createdAppointments) {
      const client = this.createdClients.find(c => c.clientId === appointment.clientId);
      const pet = this.createdPets.find(p => p.petId === appointment.petId);

      console.log(`\n🏥 Appointment ${appointment.appointmentId}:`);
      console.log(`   Client: ${client?.firstName} ${client?.lastName} (${client?.isWalkIn ? 'Walk-in' : 'Registered'})`);
      console.log(`   Pet: ${pet?.name} (${appointment.petSpecies})`);
      console.log(`   Types: ${appointment.appointmentTypes?.map(at => at.name).join(', ')}`);
      console.log(`   Status: ${appointment.status}`);
      console.log(`   Total: KES ${appointment.totalAmount}`);
    }
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up test data...');

    // Note: In a real test, you might want to clean up the test data
    // For this demo, we'll leave the data for inspection
    console.log('⚠️  Test data left in database for inspection');

    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }

  async run() {
    try {
      await this.connect();
      await this.loadReferenceData();
      await this.createTestClients();
      await this.createTestPets();
      await this.createTestAppointments();
      await this.simulateWorkflow();
      await this.printTestResults();

      console.log('\n🎉 COMPREHENSIVE APPOINTMENT WORKFLOW TEST COMPLETED SUCCESSFULLY!');
      console.log('All 4 appointments have been processed through the complete workflow.');

    } catch (error) {
      console.error('❌ Test failed:', error);
    } finally {
      await this.cleanup();
    }
  }
}

// Run the test
const tester = new AppointmentWorkflowTester();
tester.run().catch(console.error);
