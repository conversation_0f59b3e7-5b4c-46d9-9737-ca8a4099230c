const http = require('http');

const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODMxM2Q3Nzcw' +
              'YzIwMjFlOGFmZjBmNzMiLCJpYXQiOjE3NDgxNzc2NjEsImV4cCI6MTc0ODI2NDA2MX0.' +
              'ufbPUBrlbd6o7iIx-gJgu4GCYyQjfP3I6tDZBrdBpjM';

// Test data with appointmentId
const serviceData = {
  serviceName: 'Fresh Token Test Service',
  description: 'Testing with fresh token',
  defaultPrice: 300,
  estimatedDuration: 30,
  category: 'consultation',
  currency: 'KES',
  appointmentId: 7777,
  isActive: true,
  isCustom: true
};

const data = JSON.stringify(serviceData);

console.log('Testing service creation with fresh token...');
console.log('Sending data:', JSON.stringify(serviceData, null, 2));

const options = {
  hostname: 'localhost',
  port: 5500,
  path: '/api/v1/services',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
    'Content-Length': data.length
  }
};

const req = http.request(options, (res) => {
  console.log('Status:', res.statusCode);
  console.log('Headers:', JSON.stringify(res.headers, null, 2));
  
  let body = '';
  res.on('data', (chunk) => {
    body += chunk;
  });
  
  res.on('end', () => {
    console.log('Response:', body);
  });
});

req.on('error', (e) => {
  console.error('Error:', e.message);
});

req.write(data);
req.end();
