import Payment from "../models/payment.model.js";
import Invoice from "../models/invoice.model.js";
import Receipt from "../models/receipt.model.js";
import { sendResponse } from '../utils/responseHandler.js';

/**
 * Process payment for invoice
 */
export const processPayment = async (req, res) => {
    try {
        const { invoiceId } = req.params;
        const {
            amount,
            paymentMethod,
            paymentDetails = {},
            description,
            notes
        } = req.body;

        // Get invoice
        const invoice = await Invoice.findOne({ invoiceId: parseInt(invoiceId) });

        if (!invoice) {
            return sendResponse(res, 404, false, "Invoice not found");
        }

        if (invoice.paymentStatus === 'paid') {
            return sendResponse(res, 400, false, "Invoice is already fully paid");
        }

        if (amount <= 0) {
            return sendResponse(res, 400, false, "Payment amount must be greater than zero");
        }

        if (amount > invoice.amountDue) {
            return sendResponse(res, 400, false, "Payment amount cannot exceed amount due");
        }

        // Calculate transaction fee based on payment method
        let transactionFee = 0;
        if (paymentMethod === 'mpesa') {
            transactionFee = Math.max(amount * 0.01, 10); // 1% or minimum 10 KES
        } else if (paymentMethod === 'credit_card' || paymentMethod === 'debit_card') {
            transactionFee = amount * 0.025; // 2.5%
        }

        // Create payment record
        const payment = await Payment.create({
            invoiceId: invoice.invoiceId,
            appointmentId: invoice.appointmentId,
            clientId: invoice.clientId,
            clinicId: invoice.clinicId,
            amount,
            currency: invoice.currency,
            paymentMethod,
            paymentDetails,
            status: 'completed', // In real system, this might be 'pending' initially
            processedBy: req.staff?.staffId || req.user?.userId || 1001,
            transactionFee,
            description,
            notes
        });

        // Update invoice payment status
        invoice.amountPaid += amount;
        invoice.amountDue = invoice.totalAmount - invoice.amountPaid;

        if (invoice.amountDue <= 0) {
            invoice.paymentStatus = 'paid';
            invoice.status = 'paid';
        } else {
            invoice.paymentStatus = 'partial';
        }

        await invoice.save();

        // Generate receipt if fully paid
        let receipt = null;
        if (invoice.paymentStatus === 'paid') {
            receipt = await generateReceipt(invoice, payment);
        }

        return sendResponse(res, 201, true, "Payment processed successfully", {
            payment,
            invoice,
            receipt
        });
    } catch (error) {
        console.error("Process payment error:", error);
        return sendResponse(res, 500, false, `Failed to process payment: ${error.message}`);
    }
};

/**
 * Generate receipt after payment
 */
const generateReceipt = async (invoice, payment) => {
    try {
        // Get all payments for this invoice
        const allPayments = await Payment.find({
            invoiceId: invoice.invoiceId,
            status: 'completed'
        }).lean();

        // Create receipt data
        const receiptData = {
            appointmentId: invoice.appointmentId,
            clientId: invoice.clientId,
            petId: invoice.petId,
            clinicId: invoice.clinicId,

            // Services from invoice
            services: invoice.services?.map(service => ({
                serviceId: service.serviceId,
                serviceName: service.serviceName,
                description: service.description,
                quantity: service.quantity || 1,
                unitPrice: service.unitPrice,
                totalPrice: service.totalPrice
            })) || [],

            // Financial details
            subtotal: invoice.subtotal || 0,
            afterHoursCharge: invoice.afterHoursCharge || 0,
            taxRate: invoice.taxRate || 0,
            taxAmount: invoice.taxAmount || 0,
            totalAmount: invoice.totalAmount,
            currency: invoice.currency,

            // Discounts
            discounts: invoice.discounts || [],
            totalDiscounts: invoice.totalDiscounts || 0,

            // Payment information
            amountPaid: invoice.amountPaid,
            paymentMethod: payment.paymentMethod,
            paymentReference: payment.paymentReference,
            paymentDate: payment.paymentDate,
            paymentStatus: 'paid',

            // Staff information
            issuedBy: payment.processedBy,

            // Status
            status: 'issued'
        };

        // Add healthRecordId if it exists
        if (invoice.healthRecordId) {
            receiptData.healthRecordId = invoice.healthRecordId;
        }

        const receipt = await Receipt.create(receiptData);

        return receipt;
    } catch (error) {
        console.error("Generate receipt error:", error);
        throw error;
    }
};

/**
 * Get payment by ID
 */
export const getPaymentById = async (req, res) => {
    try {
        const { paymentId } = req.params;

        const payment = await Payment.findOne({
            paymentId: parseInt(paymentId)
        }).lean();

        if (!payment) {
            return sendResponse(res, 404, false, "Payment not found");
        }

        return sendResponse(res, 200, true, "Payment retrieved successfully", payment);
    } catch (error) {
        console.error("Get payment error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve payment: ${error.message}`);
    }
};

/**
 * Get payments for invoice
 */
export const getPaymentsByInvoice = async (req, res) => {
    try {
        const { invoiceId } = req.params;

        const payments = await Payment.find({
            invoiceId: parseInt(invoiceId)
        }).sort({ paymentDate: -1 }).lean();

        return sendResponse(res, 200, true, "Payments retrieved successfully", payments);
    } catch (error) {
        console.error("Get payments by invoice error:", error);
        return sendResponse(res, 500, false, `Failed to retrieve payments: ${error.message}`);
    }
};

/**
 * Refund payment
 */
export const refundPayment = async (req, res) => {
    try {
        const { paymentId } = req.params;
        const { refundAmount, refundReason } = req.body;

        const payment = await Payment.findOne({ paymentId: parseInt(paymentId) });

        if (!payment) {
            return sendResponse(res, 404, false, "Payment not found");
        }

        if (payment.status !== 'completed') {
            return sendResponse(res, 400, false, "Can only refund completed payments");
        }

        if (refundAmount > payment.amount) {
            return sendResponse(res, 400, false, "Refund amount cannot exceed payment amount");
        }

        // Update payment with refund information
        payment.refundAmount = refundAmount;
        payment.refundDate = new Date();
        payment.refundReason = refundReason;
        payment.refundedBy = req.staff?.staffId || req.user?.userId || 1001;
        payment.status = 'refunded';

        await payment.save();

        // Update invoice
        const invoice = await Invoice.findOne({ invoiceId: payment.invoiceId });
        if (invoice) {
            invoice.amountPaid -= refundAmount;
            invoice.amountDue = invoice.totalAmount - invoice.amountPaid;

            if (invoice.amountPaid === 0) {
                invoice.paymentStatus = 'pending';
            } else if (invoice.amountPaid < invoice.totalAmount) {
                invoice.paymentStatus = 'partial';
            }

            await invoice.save();
        }

        return sendResponse(res, 200, true, "Payment refunded successfully", payment);
    } catch (error) {
        console.error("Refund payment error:", error);
        return sendResponse(res, 500, false, `Failed to refund payment: ${error.message}`);
    }
};
