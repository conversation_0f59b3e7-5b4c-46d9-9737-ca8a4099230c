/**
 * Setup Test Data Script
 * Creates the necessary clinic owner and staff for testing
 */

import mongoose from 'mongoose';
import { config } from 'dotenv';
import Clinic from './models/clinic.model.js';
import Staff from './models/staff.model.js';
import User from './models/user.model.js';
import Role from './models/role.model.js';
import Species from './models/species.model.js';
import Breed from './models/breed.model.js';
import AppointmentType from './models/appointmentType.model.js';
import bcrypt from 'bcrypt';

// Load environment variables
config({ path: `.env.${process.env.NODE_ENV || 'development'}.local` });

const MONGODB_URI = process.env.DB_URI;

class TestDataSetup {
  async connect() {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  }

  async createDefaultAdmin() {
    console.log('👤 Creating default admin...');

    try {
      // Check if admin already exists
      const existingAdmin = await User.findOne({ email: '<EMAIL>' });
      if (existingAdmin) {
        console.log('⚠️  Default admin already exists');
        return existingAdmin;
      }

      const hashedPassword = await bcrypt.hash('pass123', 10);
      const admin = new User({
        firstName: 'System',
        lastName: 'Administrator',
        email: '<EMAIL>',
        password: hashedPassword,
        phoneNumber: '+254700000000',
        roleId: 1001, // Admin role
        status: 1
      });

      const savedAdmin = await admin.save();
      console.log(`✅ Created default admin: ${savedAdmin.firstName} ${savedAdmin.lastName} (ID: ${savedAdmin.userId})`);
      return savedAdmin;
    } catch (error) {
      console.error('❌ Error creating default admin:', error.message);
      throw error;
    }
  }

  async createTestClinic() {
    console.log('🏥 Creating test clinic...');

    try {
      // Check if clinic already exists
      const existingClinic = await Clinic.findOne({ clinicId: 1001 });
      if (existingClinic) {
        console.log('⚠️  Test clinic already exists');
        return existingClinic;
      }

      const clinic = new Clinic({
        clinicName: 'Adolf Veterinary Clinic',
        phoneNumber: '+254701000001',
        email: '<EMAIL>',
        address: '123 Veterinary Street, Nairobi, Kenya',
        location: {
          type: 'Point',
          coordinates: [-1.2921, 36.8219] // Nairobi coordinates
        },
        operatingHours: {
          monday: { open: '08:00', close: '18:00', isOpen: true },
          tuesday: { open: '08:00', close: '18:00', isOpen: true },
          wednesday: { open: '08:00', close: '18:00', isOpen: true },
          thursday: { open: '08:00', close: '18:00', isOpen: true },
          friday: { open: '08:00', close: '18:00', isOpen: true },
          saturday: { open: '09:00', close: '16:00', isOpen: true },
          sunday: { open: '10:00', close: '14:00', isOpen: false }
        },
        status: 1
      });

      const savedClinic = await clinic.save();
      console.log(`✅ Created test clinic: ${savedClinic.clinicName} (ID: ${savedClinic.clinicId})`);
      return savedClinic;
    } catch (error) {
      console.error('❌ Error creating test clinic:', error.message);
      throw error;
    }
  }

  async createTestStaff(clinic) {
    console.log('👨‍⚕️ Creating test staff...');

    try {
      // Check if staff already exists
      const existingStaff = await Staff.findOne({ staffId: 1001 });
      if (existingStaff) {
        console.log('⚠️  Test staff already exists');
        return existingStaff;
      }

      const hashedPassword = await bcrypt.hash('pass123', 10);
      const staff = new Staff({
        firstName: 'Dr. John',
        lastName: 'Veterinarian',
        email: '<EMAIL>',
        password: hashedPassword,
        phoneNumber: '+254701000002',
        address: '456 Staff Street, Nairobi, Kenya',
        clinicId: clinic.clinicId,
        primaryClinicId: clinic.clinicId,
        isClinicOwner: true,
        isManager: true,
        roleId: 1002, // Clinic owner role
        jobTitle: 'Chief Veterinarian',
        employmentDate: new Date(),
        salary: 100000,
        emergencyContact: {
          name: 'Jane Veterinarian',
          relationship: 'Spouse',
          phoneNumber: '+254701000003'
        },
        schedule: {
          workDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
          workHours: { start: '08:00', end: '18:00' }
        },
        status: 1
      });

      const savedStaff = await staff.save();
      console.log(`✅ Created test staff: ${savedStaff.firstName} ${savedStaff.lastName} (ID: ${savedStaff.staffId})`);
      return savedStaff;
    } catch (error) {
      console.error('❌ Error creating test staff:', error.message);
      throw error;
    }
  }

  async initializeReferenceData() {
    console.log('📚 Initializing reference data...');

    try {
      // Initialize species
      await Species.initializeSpecies();
      console.log('✅ Species initialized');

      // Initialize breeds manually
      await this.createDefaultBreeds();
      console.log('✅ Breeds initialized');

      // Initialize appointment types
      await AppointmentType.initializeAppointmentTypes();
      console.log('✅ Appointment types initialized');

    } catch (error) {
      console.error('❌ Error initializing reference data:', error.message);
      throw error;
    }
  }

  async createDefaultBreeds() {
    const defaultBreeds = [
      // Dog breeds
      { speciesName: 'Dog', breedName: 'German Shepherd', sizeCategory: 'large', lifespan: 12 },
      { speciesName: 'Dog', breedName: 'Labrador Retriever', sizeCategory: 'large', lifespan: 12 },
      { speciesName: 'Dog', breedName: 'Golden Retriever', sizeCategory: 'large', lifespan: 12 },
      { speciesName: 'Dog', breedName: 'Bulldog', sizeCategory: 'medium', lifespan: 10 },
      { speciesName: 'Dog', breedName: 'Poodle', sizeCategory: 'medium', lifespan: 14 },

      // Cat breeds
      { speciesName: 'Cat', breedName: 'Persian', sizeCategory: 'medium', lifespan: 15 },
      { speciesName: 'Cat', breedName: 'Siamese', sizeCategory: 'medium', lifespan: 15 },
      { speciesName: 'Cat', breedName: 'Maine Coon', sizeCategory: 'large', lifespan: 13 },
      { speciesName: 'Cat', breedName: 'British Shorthair', sizeCategory: 'medium', lifespan: 14 },
      { speciesName: 'Cat', breedName: 'Ragdoll', sizeCategory: 'large', lifespan: 15 }
    ];

    for (const breedData of defaultBreeds) {
      try {
        // Find the species
        const species = await Species.findOne({ speciesName: breedData.speciesName });
        if (!species) {
          console.log(`⚠️  Species ${breedData.speciesName} not found for breed ${breedData.breedName}`);
          continue;
        }

        // Check if breed already exists
        const existingBreed = await Breed.findOne({ breedName: breedData.breedName });
        if (existingBreed) {
          console.log(`⚠️  Breed ${breedData.breedName} already exists`);
          continue;
        }

        // Create the breed
        await Breed.create({
          speciesId: species.speciesId,
          breedName: breedData.breedName,
          sizeCategory: breedData.sizeCategory,
          lifespan: breedData.lifespan
        });

        console.log(`✅ Created breed: ${breedData.breedName}`);
      } catch (error) {
        console.log(`❌ Error creating breed ${breedData.breedName}:`, error.message);
      }
    }
  }

  async cleanup() {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }

  async run() {
    try {
      await this.connect();
      await this.createDefaultAdmin();
      const clinic = await this.createTestClinic();
      await this.createTestStaff(clinic);
      await this.initializeReferenceData();

      console.log('\n🎉 TEST DATA SETUP COMPLETED SUCCESSFULLY!');
      console.log('You can now run the comprehensive appointment test.');
      console.log('\nTest credentials:');
      console.log('Admin: <EMAIL> / pass123');
      console.log('Staff: <EMAIL> / pass123');

    } catch (error) {
      console.error('❌ Setup failed:', error);
      console.error('Stack trace:', error.stack);
    } finally {
      await this.cleanup();
    }
  }
}

// Run the setup
const setup = new TestDataSetup();
setup.run().catch(console.error);
