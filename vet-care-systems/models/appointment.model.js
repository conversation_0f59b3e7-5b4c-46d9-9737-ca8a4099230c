import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const appointmentSchema = new mongoose.Schema({
    // Auto-increment ID
    appointmentId: {
        type: Number,
        index: true,
        unique: true,
    },
    petId: {
        type: Number,
        required: true,
        index: true,
    },
    staffId: {
        type: Number,
        required: true,
        index: true,
    },
    clientId: {
        type: Number,
        required: true,
        index: true,
    },
    appointmentTypes: [{
        appointmentTypeId: {
            type: Number,
            required: true
        },
        price: {
            type: Number,
            required: true
        },
        currency: {
            type: String,
            required: true
        }
    }],
    dateTime: {
        type: Date,
        required: [true, "Appointment date and time is required"],
    },
    duration: {
        type: Number,
        required: [true, "Appointment duration is required"],
        min: 15,  // minimum 15 minutes
    },
    status: {
        type: String,
        enum: ["scheduled", "completed", "cancelled"],
        default: "scheduled",
    },
    reason: {
        type: String,
        required: [true, "Appointment reason is required"],
        trim: true,
    },
    notes: {
        type: String,
        trim: true,
    },
    services: [{
        serviceId: {
            type: Number,
            required: true
        },
        serviceName: {
            type: String,
            required: true
        },
        price: {
            type: Number,
            required: true
        },
        currency: {
            type: String,
            required: true
        },
        notes: {
            type: String,
            trim: true
        }
    }],
    appointmentNotes: [{
        category: {
            type: String,
            required: true
        },
        content: {
            type: String,
            required: true
        },
        isAIGenerated: {
            type: Boolean,
            default: false
        },
        createdAt: {
            type: Date,
            default: Date.now
        }
    }],
    // Veterinarian in charge of the appointment
    vetInCharge: {
        type: Number,
        ref: 'Staff',
        index: true
    },
    // Selected categories for this appointment (only show these during workflow)
    selectedCategories: [{
        category: {
            type: String,
            required: true,
            enum: [
                'consultation',
                'vaccination',
                'surgery',
                'laboratory',
                'imaging',
                'dental',
                'grooming',
                'boarding',
                'emergency',
                'wellness',
                'therapy',
                'nutrition',
                'behavioral',
                'medication',
                'follow_up',
                'other'
            ]
        },
        assignedStaff: {
            type: Number,
            ref: 'Staff',
            required: true
        },
        isCompleted: {
            type: Boolean,
            default: false
        },
        completedAt: Date,
        completedBy: {
            type: Number,
            ref: 'Staff'
        },
        signature: String, // Digital signature or confirmation
        estimatedDuration: Number, // in minutes
        actualDuration: Number, // in minutes
        priority: {
            type: String,
            enum: ['low', 'normal', 'high', 'urgent'],
            default: 'normal'
        }
    }],
    // Overall appointment completion status
    completionStatus: {
        type: String,
        enum: ['not_started', 'in_progress', 'completed', 'cancelled'],
        default: 'not_started'
    },
    // AI-generated summary from all service notes
    aiSummary: {
        content: String,
        generatedAt: Date,
        generatedBy: {
            type: Number,
            ref: 'Staff'
        },
        isApproved: {
            type: Boolean,
            default: false
        },
        approvedBy: {
            type: Number,
            ref: 'Staff'
        },
        approvedAt: Date
    }
}, { timestamps: true });

// Add auto-increment plugin
appointmentSchema.plugin(AutoIncrement, { inc_field: 'appointmentId', start_seq: 1001 });
appointmentSchema.index({ appointmentId: 1 }, { unique: true });

const Appointment = mongoose.model('Appointment', appointmentSchema);

// Function to reset collection if schema changes
const resetCollectionIfSchemaChanged = async () => {
    try {
        if (!mongoose.connection.db) {
            console.log("Waiting for database connection...");
            return;
        }

        const collectionName = Appointment.collection.name;
        const existingCollections = await mongoose.connection.db.listCollections().toArray();
        const collectionExists = existingCollections.some(col => col.name === collectionName);

        if (collectionExists) {
            console.log(`Dropping collection: ${collectionName} due to schema changes...`);
            await mongoose.connection.db.dropCollection(collectionName);
        }

        // Ensure collection is created again
        await Appointment.init();
        console.log(`Recreated collection: ${collectionName}`);
    } catch (error) {
        console.error("Error resetting collection:", error);
    }
};

// Run after Mongoose connects to DB
// mongoose.connection.once("open", resetCollectionIfSchemaChanged);

export default Appointment;