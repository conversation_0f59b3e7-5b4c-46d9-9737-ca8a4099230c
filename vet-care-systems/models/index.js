// Import all models to ensure they are registered with Mongoose
import './user.model.js';
import './staff.model.js';
import './clinic.model.js';
import './client.model.js';
import './pet.model.js';
import './species.model.js';
import './breed.model.js';
import './appointment.model.js';
import './appointmentType.model.js';
import './appointmentNote.model.js';
import './appointmentService.model.js';
import './service.model.js';
import './serviceType.model.js';
import './role.model.js';
import './permission.model.js';
import './healthRecord.model.js';
import './clientClinicRelationship.model.js';
import './petClinicRelationship.model.js';
import './inventory.model.js';
import './medicationDispensing.model.js';
import './invoice.model.js';
import './payment.model.js';
import './receipt.model.js';
import './discount.model.js';

// Import new models
import './aiSuggestion.model.js';

console.log('All models loaded successfully');
