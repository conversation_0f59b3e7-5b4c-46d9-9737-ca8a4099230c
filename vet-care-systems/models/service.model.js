import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const serviceSchema = new mongoose.Schema({
    // Auto-increment ID
    serviceId: {
        type: Number,
        index: true,
        unique: true,
    },
    serviceName: {
        type: String,
        required: true,
        trim: true,
        maxlength: 200,
        index: true
    },
    serviceCode: {
        type: String,
        trim: true,
        maxlength: 50,
        index: true
    },
    description: {
        type: String,
        trim: true,
        maxlength: 1000
    },
    category: {
        type: String,
        required: true,
        enum: [
            'consultation',
            'vaccination',
            'surgery',
            'laboratory',
            'imaging',
            'dental',
            'grooming',
            'boarding',
            'emergency',
            'wellness',
            'therapy',
            'nutrition',
            'behavioral',
            'medication',
            'follow_up',
            'other'
        ],
        index: true
    },
    subcategory: {
        type: String,
        trim: true,
        maxlength: 100
    },
    defaultPrice: {
        type: Number,
        required: true,
        min: 0
    },
    currency: {
        type: String,
        required: true,
        default: 'KES',
        enum: ['KES', 'USD', 'EUR', 'GBP']
    },
    priceRange: {
        min: {
            type: Number,
            min: 0
        },
        max: {
            type: Number,
            min: 0
        }
    },
    estimatedDuration: {
        type: Number, // in minutes
        min: 0,
        default: 30
    },
    durationRange: {
        min: Number, // in minutes
        max: Number  // in minutes
    },
    requiresAnesthesia: {
        type: Boolean,
        default: false
    },
    requiresSpecialist: {
        type: Boolean,
        default: false
    },
    specialistType: {
        type: String,
        enum: ['surgeon', 'cardiologist', 'dermatologist', 'oncologist', 'neurologist', 'ophthalmologist', 'other']
    },
    equipmentRequired: [{
        equipmentName: String,
        isEssential: {
            type: Boolean,
            default: true
        }
    }],
    materialsRequired: [{
        materialName: String,
        estimatedQuantity: Number,
        unit: String,
        isEssential: {
            type: Boolean,
            default: true
        }
    }],
    prerequisites: [{
        type: String,
        trim: true
    }],
    contraindications: [{
        type: String,
        trim: true
    }],
    followUpRequired: {
        type: Boolean,
        default: false
    },
    followUpDays: {
        type: Number,
        min: 0
    },
    riskLevel: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'low'
    },
    ageRestrictions: {
        minAge: Number, // in months
        maxAge: Number  // in months
    },
    speciesRestrictions: [{
        type: String,
        trim: true
    }],
    breedRestrictions: [{
        type: String,
        trim: true
    }],
    isActive: {
        type: Boolean,
        default: true,
        index: true
    },
    isCustom: {
        type: Boolean,
        default: false,
        index: true
    },
    clinicId: {
        type: Number,
        index: true
    },
    appointmentId: {
        type: Number,
        index: true
    },
    createdBy: {
        type: Number,
        required: true,
        index: true
    },
    modifiedBy: {
        type: Number,
        index: true
    },
    tags: [{
        type: String,
        trim: true,
        lowercase: true
    }],
    instructions: {
        preService: String,
        duringService: String,
        postService: String
    },
    billing: {
        isInsuranceCovered: {
            type: Boolean,
            default: false
        },
        insuranceCode: String,
        taxable: {
            type: Boolean,
            default: true
        },
        taxRate: {
            type: Number,
            min: 0,
            max: 100,
            default: 0
        }
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Add auto-increment plugin
serviceSchema.plugin(AutoIncrement, { inc_field: 'serviceId', start_seq: 1001 });
serviceSchema.index({ serviceId: 1 }, { unique: true });

// Indexes for efficient queries
serviceSchema.index({ category: 1, isActive: 1 });
serviceSchema.index({ serviceName: 'text', description: 'text', tags: 'text' });
serviceSchema.index({ clinicId: 1, isActive: 1 });
serviceSchema.index({ createdBy: 1, isCustom: 1 });
serviceSchema.index({ defaultPrice: 1, category: 1 });
serviceSchema.index({ requiresSpecialist: 1, specialistType: 1 });
serviceSchema.index({ appointmentId: 1, isActive: 1 });

// Virtual for created by staff data
serviceSchema.virtual('createdByData', {
    ref: 'Staff',
    localField: 'createdBy',
    foreignField: 'staffId',
    justOne: true
});

// Pre-save middleware to validate price range
serviceSchema.pre('save', function(next) {
    if (this.priceRange && this.priceRange.min && this.priceRange.max) {
        if (this.priceRange.min > this.priceRange.max) {
            return next(new Error('Price range minimum cannot be greater than maximum'));
        }
        // Ensure default price is within range
        if (this.defaultPrice < this.priceRange.min || this.defaultPrice > this.priceRange.max) {
            this.defaultPrice = Math.max(this.priceRange.min, Math.min(this.priceRange.max, this.defaultPrice));
        }
    }

    if (this.durationRange && this.durationRange.min && this.durationRange.max) {
        if (this.durationRange.min > this.durationRange.max) {
            return next(new Error('Duration range minimum cannot be greater than maximum'));
        }
        // Ensure estimated duration is within range
        if (this.estimatedDuration < this.durationRange.min || this.estimatedDuration > this.durationRange.max) {
            this.estimatedDuration = Math.max(this.durationRange.min, Math.min(this.durationRange.max, this.estimatedDuration));
        }
    }

    next();
});

// Static method to get services by category
serviceSchema.statics.getByCategory = function(category, clinicId = null) {
    const query = { category, isActive: true };
    if (clinicId) {
        query.$or = [
            { clinicId: null }, // Global services
            { clinicId: clinicId } // Clinic-specific services
        ];
    }
    return this.find(query).sort({ serviceName: 1 });
};

// Static method to search services
serviceSchema.statics.searchServices = function(searchTerm, category = null, clinicId = null) {
    const query = {
        $text: { $search: searchTerm },
        isActive: true
    };

    if (category) query.category = category;
    if (clinicId) {
        query.$or = [
            { clinicId: null },
            { clinicId: clinicId }
        ];
    }

    return this.find(query, { score: { $meta: 'textScore' } })
               .sort({ score: { $meta: 'textScore' } });
};

const Service = mongoose.model('Service', serviceSchema);

export default Service;
