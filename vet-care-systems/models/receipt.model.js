import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

/**
 * Receipt Model
 *
 * This model tracks receipts for health records and appointments
 */
const receiptSchema = new mongoose.Schema({
    // Auto-increment ID
    receiptId: {
        type: Number,
        index: true,
        unique: true,
    },

    // Receipt number (human-readable)
    receiptNumber: {
        type: String,
        required: true,
        unique: true,
        index: true
    },

    // Related records
    healthRecordId: {
        type: Number,
        required: false, // Make optional for appointment-based receipts
        index: true
    },
    appointmentId: {
        type: Number,
        index: true
    },

    // Client and pet information
    clientId: {
        type: Number,
        required: true,
        index: true
    },
    petId: {
        type: Number,
        required: true,
        index: true
    },

    // Clinic information
    clinicId: {
        type: Number,
        required: true,
        index: true
    },

    // Service details
    services: [{
        serviceId: {
            type: Number,
            required: true
        },
        serviceName: {
            type: String,
            required: true
        },
        description: String,
        quantity: {
            type: Number,
            default: 1,
            min: 1
        },
        unitPrice: {
            type: Number,
            required: true,
            min: 0
        },
        totalPrice: {
            type: Number,
            required: true,
            min: 0
        }
    }],

    // Financial details
    subtotal: {
        type: Number,
        required: true,
        min: 0
    },
    totalDiscount: {
        type: Number,
        default: 0,
        min: 0
    },
    tax: {
        type: Number,
        default: 0,
        min: 0
    },
    totalAmount: {
        type: Number,
        required: true,
        min: 0
    },
    amountPaid: {
        type: Number,
        default: 0,
        min: 0
    },
    balance: {
        type: Number,
        default: 0
    },

    // Currency
    currency: {
        type: String,
        required: true,
        enum: ['KES', 'USD', 'EUR', 'GBP', 'CNY', 'JPY', 'AUD', 'CAD', 'CHF', 'AED'],
        default: 'KES'
    },

    // Discounts applied
    discounts: [{
        discountId: {
            type: Number,
            required: true
        },
        discountType: String,
        discountValue: Number,
        discountAmount: Number,
        reason: String
    }],

    // Payment information
    paymentMethod: {
        type: String,
        enum: ['cash', 'card', 'mobile_money', 'bank_transfer', 'insurance', 'credit'],
        default: 'cash'
    },
    paymentStatus: {
        type: String,
        enum: ['paid', 'partially_paid', 'pending', 'overdue', 'waived'],
        default: 'pending'
    },
    paymentDate: Date,

    // Staff information
    issuedBy: {
        type: Number,
        required: true,
        index: true
    },

    // Receipt status
    status: {
        type: String,
        enum: ['draft', 'issued', 'cancelled', 'refunded'],
        default: 'draft'
    },

    // Dates
    issueDate: {
        type: Date,
        default: Date.now
    },
    dueDate: Date,

    // Additional information
    notes: {
        type: String,
        trim: true
    },

    // Receipt template and formatting
    template: {
        type: String,
        default: 'standard'
    },

    // Digital receipt information
    emailSent: {
        type: Boolean,
        default: false
    },
    emailSentDate: Date,
    smsSent: {
        type: Boolean,
        default: false
    },
    smsSentDate: Date
}, { timestamps: true });

// Add indexes for better query performance
receiptSchema.index({ receiptNumber: 1 });
receiptSchema.index({ healthRecordId: 1 });
receiptSchema.index({ appointmentId: 1 });
receiptSchema.index({ clientId: 1 });
receiptSchema.index({ petId: 1 });
receiptSchema.index({ clinicId: 1 });
receiptSchema.index({ issuedBy: 1 });
receiptSchema.index({ paymentStatus: 1 });
receiptSchema.index({ status: 1 });
receiptSchema.index({ issueDate: -1 });
receiptSchema.index({ createdAt: -1 });

// Pre-save middleware to generate receipt number
receiptSchema.pre('save', async function(next) {
    if (this.isNew && !this.receiptNumber) {
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        // Find the last receipt for today
        const lastReceipt = await this.constructor.findOne({
            receiptNumber: new RegExp(`^RCP-${year}${month}${day}-`)
        }).sort({ receiptNumber: -1 });

        let sequence = 1;
        if (lastReceipt) {
            const lastSequence = parseInt(lastReceipt.receiptNumber.split('-')[2]);
            sequence = lastSequence + 1;
        }

        this.receiptNumber = `RCP-${year}${month}${day}-${String(sequence).padStart(4, '0')}`;
    }
    next();
});

// Add auto-increment plugin
receiptSchema.plugin(AutoIncrement, { inc_field: 'receiptId', start_seq: 1001 });
receiptSchema.index({ receiptId: 1 }, { unique: true });

const Receipt = mongoose.model('Receipt', receiptSchema);

// Function to reset collection if schema changes
const resetCollectionIfSchemaChanged = async () => {
    try {
        if (!mongoose.connection.db) {
            console.log("Waiting for database connection...");
            return;
        }

        const collectionName = Receipt.collection.name;
        const existingCollections = await mongoose.connection.db.listCollections().toArray();
        const collectionExists = existingCollections.some(col => col.name === collectionName);

        if (collectionExists) {
            console.log(`Dropping collection: ${collectionName} due to schema changes...`);
            await mongoose.connection.db.dropCollection(collectionName);
        }

        // Ensure collection is created again
        await Receipt.init();
        console.log(`Recreated collection: ${collectionName}`);
    } catch (error) {
        console.error("Error resetting collection:", error);
    }
};

// Run after Mongoose connects to DB
// mongoose.connection.once("open", resetCollectionIfSchemaChanged);

export default Receipt;
