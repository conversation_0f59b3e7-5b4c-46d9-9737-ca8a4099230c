import mongoose from 'mongoose';
import AutoIncrementFactory from 'mongoose-sequence';

const AutoIncrement = AutoIncrementFactory(mongoose);

const aiSuggestionSchema = new mongoose.Schema({
    // Auto-increment ID
    suggestionId: {
        type: Number,
        index: true,
        unique: true,
    },
    appointmentId: {
        type: Number,
        required: true,
        ref: 'Appointment',
        index: true,
    },
    petId: {
        type: Number,
        required: true,
        ref: 'Pet',
        index: true,
    },
    category: {
        type: String,
        required: true,
        enum: [
            'diagnosis',
            'treatment',
            'medication',
            'follow_up',
            'prevention',
            'nutrition',
            'behavioral',
            'emergency',
            'surgery',
            'laboratory',
            'imaging'
        ],
        index: true
    },
    suggestionType: {
        type: String,
        required: true,
        enum: [
            'medication_recommendation',
            'treatment_plan',
            'diagnostic_test',
            'follow_up_care',
            'preventive_measure',
            'dietary_recommendation',
            'behavioral_intervention',
            'emergency_protocol',
            'surgical_consideration',
            'alternative_treatment'
        ]
    },
    title: {
        type: String,
        required: true,
        trim: true,
        maxlength: 200
    },
    description: {
        type: String,
        required: true,
        trim: true,
        maxlength: 2000
    },
    // AI confidence score (0-100)
    confidenceScore: {
        type: Number,
        min: 0,
        max: 100,
        required: true
    },
    // Priority level
    priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'medium'
    },
    // Suggested medications
    medications: [{
        name: {
            type: String,
            required: true,
            trim: true
        },
        dosage: {
            type: String,
            required: true,
            trim: true
        },
        frequency: {
            type: String,
            required: true,
            trim: true
        },
        duration: {
            type: String,
            required: true,
            trim: true
        },
        instructions: {
            type: String,
            trim: true
        },
        contraindications: [{
            type: String,
            trim: true
        }],
        sideEffects: [{
            type: String,
            trim: true
        }]
    }],
    // Suggested treatments
    treatments: [{
        name: {
            type: String,
            required: true,
            trim: true
        },
        description: {
            type: String,
            trim: true
        },
        estimatedDuration: Number, // in minutes
        estimatedCost: Number,
        currency: {
            type: String,
            default: 'KES'
        },
        requirements: [{
            type: String,
            trim: true
        }],
        risks: [{
            type: String,
            trim: true
        }]
    }],
    // Diagnostic tests suggested
    diagnosticTests: [{
        testName: {
            type: String,
            required: true,
            trim: true
        },
        reason: {
            type: String,
            required: true,
            trim: true
        },
        urgency: {
            type: String,
            enum: ['routine', 'urgent', 'emergency'],
            default: 'routine'
        },
        estimatedCost: Number,
        expectedResults: {
            type: String,
            trim: true
        }
    }],
    // Follow-up recommendations
    followUp: {
        recommended: {
            type: Boolean,
            default: false
        },
        timeframe: {
            type: String,
            trim: true
        },
        reason: {
            type: String,
            trim: true
        },
        specificInstructions: {
            type: String,
            trim: true
        }
    },
    // AI model information
    aiModel: {
        provider: {
            type: String,
            enum: ['openai', 'anthropic', 'google', 'custom'],
            required: true
        },
        model: {
            type: String,
            required: true
        },
        version: String,
        temperature: Number,
        maxTokens: Number
    },
    // Context used for generation
    context: {
        symptoms: [{
            type: String,
            trim: true
        }],
        vitalSigns: {
            temperature: Number,
            heartRate: Number,
            respiratoryRate: Number,
            weight: Number,
            bloodPressure: String
        },
        previousConditions: [{
            type: String,
            trim: true
        }],
        currentMedications: [{
            type: String,
            trim: true
        }],
        allergies: [{
            type: String,
            trim: true
        }]
    },
    // Staff interaction
    generatedBy: {
        type: Number,
        ref: 'Staff',
        required: true
    },
    reviewedBy: {
        type: Number,
        ref: 'Staff'
    },
    reviewedAt: Date,
    status: {
        type: String,
        enum: ['pending', 'reviewed', 'accepted', 'rejected', 'implemented'],
        default: 'pending'
    },
    reviewNotes: {
        type: String,
        trim: true,
        maxlength: 1000
    },
    // Implementation tracking
    implementedBy: {
        type: Number,
        ref: 'Staff'
    },
    implementedAt: Date,
    implementationNotes: {
        type: String,
        trim: true,
        maxlength: 1000
    },
    // Outcome tracking
    outcome: {
        effectiveness: {
            type: String,
            enum: ['very_effective', 'effective', 'somewhat_effective', 'not_effective'],
        },
        sideEffects: [{
            type: String,
            trim: true
        }],
        patientResponse: {
            type: String,
            trim: true
        },
        notes: {
            type: String,
            trim: true
        }
    }
}, { timestamps: true });

// Add auto-increment plugin
aiSuggestionSchema.plugin(AutoIncrement, { inc_field: 'suggestionId', start_seq: 1001 });

// Indexes for better query performance
aiSuggestionSchema.index({ suggestionId: 1 }, { unique: true });
aiSuggestionSchema.index({ appointmentId: 1 });
aiSuggestionSchema.index({ petId: 1 });
aiSuggestionSchema.index({ category: 1 });
aiSuggestionSchema.index({ suggestionType: 1 });
aiSuggestionSchema.index({ status: 1 });
aiSuggestionSchema.index({ priority: 1 });
aiSuggestionSchema.index({ generatedBy: 1 });
aiSuggestionSchema.index({ reviewedBy: 1 });
aiSuggestionSchema.index({ createdAt: -1 });

// Virtual for populated staff data
aiSuggestionSchema.virtual('generatedByStaff', {
    ref: 'Staff',
    localField: 'generatedBy',
    foreignField: 'staffId',
    justOne: true
});

aiSuggestionSchema.virtual('reviewedByStaff', {
    ref: 'Staff',
    localField: 'reviewedBy',
    foreignField: 'staffId',
    justOne: true
});

// Ensure virtual fields are serialized
aiSuggestionSchema.set('toJSON', { virtuals: true });
aiSuggestionSchema.set('toObject', { virtuals: true });

// Comment out the line that drops and recreates the collection
// await mongoose.connection.db.dropCollection('aisuggestions').catch(() => {});

const AISuggestion = mongoose.model('AISuggestion', aiSuggestionSchema);

export default AISuggestion;
