/**
 * API v1 Routes
 *
 * This file centralizes all v1 API routes.
 */

import { Router } from "express";
import authRouter from "./auth.routes.js";
import csrfRouter from "./csrf.routes.js";

// Import all route modules
import appointmentRouter from "../appointment.routes.js";
import clientRouter from "../client.routes.js";
import petRouter from "../pet.routes.js";
import clinicRouter from "../clinic.routes.js";
import staffRouter from "../staff.routes.js";
import serviceRouter from "../service.routes.js";
import healthRecordRouter from "../healthRecord.routes.js";
import invoiceRouter from "../invoice.routes.js";
import paymentRouter from "../payment.routes.js";
import receiptRouter from "../receipt.routes.js";
import workflowRouter from "../workflow.routes.js";

const v1Router = Router();

// Mount CSRF routes first to ensure token availability
v1Router.use('/', csrfRouter);

// Mount all resource routers
v1Router.use('/auth', authRouter);
v1Router.use('/appointments', appointmentRouter);
v1Router.use('/clients', clientRouter);
v1Router.use('/pets', petRouter);
v1Router.use('/clinics', clinicRouter);
v1Router.use('/staff', staffRouter);
v1Router.use('/services', serviceRouter);
v1Router.use('/health-records', healthRecordRouter);
v1Router.use('/invoices', invoiceRouter);
v1Router.use('/payments', paymentRouter);
v1Router.use('/receipts', receiptRouter);
v1Router.use('/workflow', workflowRouter);

export default v1Router;
