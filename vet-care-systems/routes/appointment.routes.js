import { Router } from "express";
import { verifyToken } from "../middleware/auth.middleware.js";
import {
    createAppointment,
    getAllAppointments,
    getAppointmentById,
    updateAppointment,
    deleteAppointment,
    getAppointmentTypes,
    completeAppointment,
    reassignAppointment,
    updateAppointmentProgress,
} from "../controllers/appointment.controller.js";

// Import task management controllers
import {
    assignCategoriesToStaff,
    completeCategoryTask,
    getAppointmentTaskStatus,
    reassignCategory,
    generateAppointmentSummary
} from "../controllers/appointmentTask.controller.js";

// Import AI suggestion controllers
// import {
//     generateAISuggestions,
//     getAppointmentSuggestions,
//     reviewSuggestion,
//     implementSuggestion,
//     recordOutcome
// } from "../controllers/aiSuggestion.controller.js";

const appointmentRouter = Router();

// Apply authentication to all routes
appointmentRouter.use(verifyToken);

appointmentRouter.get('/types', getAppointmentTypes);
appointmentRouter.post('/', createAppointment);
appointmentRouter.get('/', getAllAppointments);

// Progress routes
appointmentRouter.put('/:appointmentId/progress', updateAppointmentProgress);

appointmentRouter.post('/:appointmentId/complete', completeAppointment);
appointmentRouter.put('/:appointmentId/reassign', reassignAppointment);

// Task management routes
appointmentRouter.post('/:appointmentId/assign-categories', assignCategoriesToStaff);
appointmentRouter.post('/:appointmentId/categories/:category/complete', completeCategoryTask);
appointmentRouter.get('/:appointmentId/task-status', getAppointmentTaskStatus);
appointmentRouter.put('/:appointmentId/categories/:category/reassign', reassignCategory);
appointmentRouter.post('/:appointmentId/generate-summary', generateAppointmentSummary);

// AI suggestion routes
// appointmentRouter.post('/:appointmentId/ai-suggestions', generateAISuggestions);
// appointmentRouter.get('/:appointmentId/ai-suggestions', getAppointmentSuggestions);

appointmentRouter.get('/:appointmentId', getAppointmentById);
appointmentRouter.put('/:appointmentId', updateAppointment);
appointmentRouter.delete('/:appointmentId', deleteAppointment);

// AI suggestion management routes (separate from appointment-specific routes)
// appointmentRouter.put('/suggestions/:suggestionId/review', reviewSuggestion);
// appointmentRouter.put('/suggestions/:suggestionId/implement', implementSuggestion);
// appointmentRouter.put('/suggestions/:suggestionId/outcome', recordOutcome);

export default appointmentRouter;