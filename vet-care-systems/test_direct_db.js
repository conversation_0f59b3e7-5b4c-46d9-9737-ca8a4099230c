// Direct database test to check if appointmentId is being saved
import mongoose from 'mongoose';
import Service from './models/service.model.js';

// Connect to MongoDB
const MONGODB_URI = "mongodb+srv://kabiujm:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";

async function testDirectDB() {
    try {
        console.log('Connecting to MongoDB...');
        await mongoose.connect(MONGODB_URI);
        console.log('Connected to MongoDB');

        // Test data with appointmentId
        const testServiceData = {
            serviceName: 'Direct DB Test Service',
            description: 'Testing appointmentId directly in database',
            category: 'consultation',
            defaultPrice: 500,
            currency: 'KES',
            estimatedDuration: 60,
            appointmentId: 9999,
            isActive: true,
            isCustom: true,
            createdBy: 1087
        };

        console.log('Creating service with data:', JSON.stringify(testServiceData, null, 2));

        // Create service directly using Mongoose
        const service = await Service.create(testServiceData);
        console.log('Service created successfully!');
        console.log('Created service ID:', service.serviceId);
        console.log('appointmentId in created service:', service.appointmentId);

        // Fetch the service back from database
        const fetchedService = await Service.findOne({ serviceId: service.serviceId });
        console.log('\nFetched service from database:');
        console.log('appointmentId in fetched service:', fetchedService.appointmentId);
        console.log('Full fetched service:', JSON.stringify(fetchedService.toObject(), null, 2));

        // Test filtering by appointmentId
        const filteredServices = await Service.find({ appointmentId: 9999 });
        console.log('\nServices filtered by appointmentId 9999:', filteredServices.length);
        if (filteredServices.length > 0) {
            console.log('First filtered service appointmentId:', filteredServices[0].appointmentId);
        }

        // Clean up - delete the test service
        await Service.deleteOne({ serviceId: service.serviceId });
        console.log('\nTest service deleted');

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await mongoose.disconnect();
        console.log('Disconnected from MongoDB');
    }
}

testDirectDB();
