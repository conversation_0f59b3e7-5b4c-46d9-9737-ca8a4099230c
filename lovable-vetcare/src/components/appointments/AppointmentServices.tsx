import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import {
  Plus,
  Edit,
  Trash2,
  DollarSign,
  Clock,
  User,
  Tag,
  CheckCircle,
  AlertCircle,
  Play,
  Pause,
  MessageSquare,
  Wand2,
  Save,
  Filter,
  FileText,
  Stethoscope,
  Syringe,
  Heart,
  Activity
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/services/api';

interface AppointmentService {
  appointmentServiceId: number;
  appointmentId: number;
  serviceId: number;
  serviceName: string;
  serviceDescription?: string;
  category: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  finalAmount: number;
  currency: string;
  discount: {
    percentage: number;
    amount: number;
    reason?: string;
  };
  status: string;
  performedByName?: string;
  notes?: string;
  createdAt: string;
}

interface AppointmentServicesProps {
  appointmentId: number;
}

const serviceCategories = [
  { value: 'consultation', label: 'Consultation' },
  { value: 'vaccination', label: 'Vaccination' },
  { value: 'surgery', label: 'Surgery' },
  { value: 'laboratory', label: 'Laboratory' },
  { value: 'imaging', label: 'Imaging' },
  { value: 'dental', label: 'Dental' },
  { value: 'grooming', label: 'Grooming' },
  { value: 'boarding', label: 'Boarding' },
  { value: 'emergency', label: 'Emergency' },
  { value: 'wellness', label: 'Wellness' },
  { value: 'therapy', label: 'Therapy' },
  { value: 'nutrition', label: 'Nutrition' },
  { value: 'behavioral', label: 'Behavioral' },
  { value: 'medication', label: 'Medication' },
  { value: 'follow_up', label: 'Follow-up' },
  { value: 'other', label: 'Other' }
];

const AppointmentServices: React.FC<AppointmentServicesProps> = ({ appointmentId }) => {
  const [isAddingService, setIsAddingService] = useState(false);
  const [isCreatingNewService, setIsCreatingNewService] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('consultation');
  const [selectedServiceId, setSelectedServiceId] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [isNotesModalOpen, setIsNotesModalOpen] = useState(false);
  const [selectedServiceForNotes, setSelectedServiceForNotes] = useState<AppointmentService | null>(null);
  const [noteContent, setNoteContent] = useState('');
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const [newService, setNewService] = useState({
    serviceId: '',
    serviceName: '',
    serviceDescription: '',
    category: 'consultation',
    quantity: 1,
    unitPrice: 0,
    currency: 'KES',
    discount: { percentage: 0, amount: 0, reason: '' },
    notes: '',
    isCustomService: false
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch appointment services
  const { data: servicesResponse, isLoading } = useQuery({
    queryKey: ['appointmentServices', appointmentId],
    queryFn: async () => {
      const response = await api.get(`/appointment/${appointmentId}/services`);
      return response.data;
    }
  });

  // Fetch available services by category
  const { data: availableServicesResponse, isLoading: isLoadingServices } = useQuery({
    queryKey: ['services', 'category', selectedCategory, appointmentId],
    queryFn: async () => {
      const response = await api.get(`/services?category=${selectedCategory}&limit=100&appointmentId=${appointmentId}`);
      return response.data;
    },
    enabled: isAddingService && !isCreatingNewService
  });

  // Create new service mutation
  const createServiceMutation = useMutation({
    mutationFn: async (serviceData: any) => {
      const response = await api.post('/services', serviceData);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['services'] });
      // Auto-select the newly created service
      setSelectedServiceId(data.data.serviceId.toString());
      setNewService(prev => ({
        ...prev,
        serviceId: data.data.serviceId.toString(),
        serviceName: data.data.serviceName,
        serviceDescription: data.data.description || '',
        unitPrice: data.data.defaultPrice,
        isCustomService: true
      }));
      setIsCreatingNewService(false);
      toast({
        title: "Success",
        description: "New service created successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to create service",
        variant: "destructive",
      });
    }
  });

  // Add service mutation
  const addServiceMutation = useMutation({
    mutationFn: async (serviceData: any) => {
      const response = await api.post(`/appointment/${appointmentId}/services`, serviceData);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointmentServices', appointmentId] });
      setIsAddingService(false);
      setIsCreatingNewService(false);
      setSelectedServiceId('');
      setNewService({
        serviceId: '',
        serviceName: '',
        serviceDescription: '',
        category: 'consultation',
        quantity: 1,
        unitPrice: 0,
        currency: 'KES',
        discount: { percentage: 0, amount: 0, reason: '' },
        notes: '',
        isCustomService: false
      });
      toast({
        title: "Success",
        description: "Service added successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to add service",
        variant: "destructive",
      });
    }
  });

  // Update service status mutation
  const updateStatusMutation = useMutation({
    mutationFn: async ({ serviceId, status }: { serviceId: number; status: string }) => {
      const response = await api.patch(`/services/${serviceId}/status`, { status });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointmentServices', appointmentId] });
      toast({
        title: "Success",
        description: "Service status updated",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to update status",
        variant: "destructive",
      });
    }
  });

  // Update service notes mutation
  const updateNotesMutation = useMutation({
    mutationFn: async ({ serviceId, notes }: { serviceId: number; notes: string }) => {
      const response = await api.patch(`/appointment-services/${serviceId}/notes`, { notes });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointmentServices', appointmentId] });
      setIsNotesModalOpen(false);
      setSelectedServiceForNotes(null);
      setNoteContent('');
      toast({
        title: "Success",
        description: "Service notes updated successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to update notes",
        variant: "destructive",
      });
    }
  });

  // AI note generation mutation
  const generateAINotesMutation = useMutation({
    mutationFn: async ({ serviceData, appointmentData }: { serviceData: AppointmentService; appointmentData: any }) => {
      const response = await api.post('/ai/generate-service-notes', {
        service: serviceData,
        appointment: appointmentData,
        context: 'veterinary_service'
      });
      return response.data;
    },
    onSuccess: (data) => {
      setNoteContent(data.data.generatedNotes || '');
      setIsGeneratingAI(false);
      toast({
        title: "Success",
        description: "AI notes generated successfully",
      });
    },
    onError: (error: any) => {
      setIsGeneratingAI(false);
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to generate AI notes",
        variant: "destructive",
      });
    }
  });

  const handleCreateNewService = () => {
    if (!newService.serviceName || !newService.unitPrice) {
      toast({
        title: "Error",
        description: "Service name and unit price are required",
        variant: "destructive",
      });
      return;
    }

    const serviceData = {
      serviceName: newService.serviceName,
      description: newService.serviceDescription,
      category: newService.category,
      defaultPrice: parseFloat(newService.unitPrice.toString()),
      currency: newService.currency,
      isCustom: true,
      isActive: true
    };

    createServiceMutation.mutate(serviceData);
  };

  const handleSubmitService = () => {
    if (isCreatingNewService) {
      handleCreateNewService();
      return;
    }

    if (!newService.serviceName || !newService.unitPrice) {
      toast({
        title: "Error",
        description: "Service name and unit price are required",
        variant: "destructive",
      });
      return;
    }

    const serviceData = {
      ...newService,
      serviceId: parseInt(newService.serviceId) || Date.now(),
      unitPrice: parseFloat(newService.unitPrice.toString()),
      quantity: parseInt(newService.quantity.toString())
    };

    addServiceMutation.mutate(serviceData);
  };

  const handleServiceSelect = (serviceId: string) => {
    const availableServices = availableServicesResponse?.data || [];
    const selectedService = availableServices.find((s: any) => s.serviceId.toString() === serviceId);

    if (selectedService) {
      setNewService(prev => ({
        ...prev,
        serviceId: serviceId,
        serviceName: selectedService.serviceName,
        serviceDescription: selectedService.description || '',
        unitPrice: selectedService.defaultPrice,
        category: selectedService.category,
        isCustomService: false
      }));
    }
    setSelectedServiceId(serviceId);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'in_progress':
        return <Play className="h-4 w-4 text-blue-500" />;
      case 'pending':
        return <Pause className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'consultation':
        return <Stethoscope className="h-4 w-4" />;
      case 'vaccination':
        return <Syringe className="h-4 w-4" />;
      case 'surgery':
        return <Heart className="h-4 w-4" />;
      case 'laboratory':
        return <Activity className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const handleOpenNotesModal = (service: AppointmentService) => {
    setSelectedServiceForNotes(service);
    setNoteContent(service.notes || '');
    setIsNotesModalOpen(true);
  };

  const handleSaveNotes = () => {
    if (selectedServiceForNotes) {
      updateNotesMutation.mutate({
        serviceId: selectedServiceForNotes.appointmentServiceId,
        notes: noteContent
      });
    }
  };

  const handleGenerateAINotes = () => {
    if (selectedServiceForNotes) {
      setIsGeneratingAI(true);
      generateAINotesMutation.mutate({
        serviceData: selectedServiceForNotes,
        appointmentData: { appointmentId } // You might want to pass more appointment data
      });
    }
  };

  const groupServicesByCategory = (services: AppointmentService[]) => {
    const filtered = filterCategory === 'all'
      ? services
      : services.filter(service => service.category === filterCategory);

    return filtered.reduce((groups, service) => {
      const category = service.category;
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(service);
      return groups;
    }, {} as Record<string, AppointmentService[]>);
  };

  const services = servicesResponse?.data?.services || [];
  const totals = servicesResponse?.data?.totals || {
    subtotal: 0,
    totalDiscount: 0,
    grandTotal: 0
  };

  const groupedServices = groupServicesByCategory(services);
  const categories = Object.keys(groupedServices);

  return (
    <div className="space-y-4">
      {/* Header with add button and filter */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Appointment Services
          </h3>
          {services.length > 0 && (
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <Select value={filterCategory} onValueChange={setFilterCategory}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {serviceCategories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        <Dialog open={isAddingService} onOpenChange={setIsAddingService}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Service
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add Service to Appointment</DialogTitle>
              <DialogDescription>
                {isCreatingNewService
                  ? "Create a new service and add it to this appointment."
                  : "Select an existing service or create a new one for this appointment."
                }
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              {/* Service Selection Mode Toggle */}
              <div className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="existing-service"
                    name="service-mode"
                    checked={!isCreatingNewService}
                    onChange={() => setIsCreatingNewService(false)}
                    className="rounded border-gray-300"
                  />
                  <label htmlFor="existing-service" className="text-sm font-medium">
                    Select Existing Service
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="new-service"
                    name="service-mode"
                    checked={isCreatingNewService}
                    onChange={() => setIsCreatingNewService(true)}
                    className="rounded border-gray-300"
                  />
                  <label htmlFor="new-service" className="text-sm font-medium">
                    Create New Service
                  </label>
                </div>
              </div>

              {/* Category Selection */}
              <div>
                <Label htmlFor="category">Category *</Label>
                <Select
                  value={selectedCategory}
                  onValueChange={(value) => {
                    setSelectedCategory(value);
                    setNewService({...newService, category: value});
                    setSelectedServiceId('');
                  }}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {serviceCategories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Existing Service Selection */}
              {!isCreatingNewService && (
                <div>
                  <Label htmlFor="existingService">Select Service *</Label>
                  {isLoadingServices ? (
                    <div className="p-3 text-center text-gray-500">Loading services...</div>
                  ) : (
                    <Select value={selectedServiceId} onValueChange={handleServiceSelect}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a service from this category" />
                      </SelectTrigger>
                      <SelectContent>
                        {(availableServicesResponse?.data || []).map((service: any) => (
                          <SelectItem key={service.serviceId} value={service.serviceId.toString()}>
                            <div className="flex flex-col">
                              <span className="font-medium">{service.serviceName}</span>
                              <span className="text-sm text-gray-500">
                                {service.currency} {service.defaultPrice} • {service.estimatedDuration || 30} min
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                        {(availableServicesResponse?.data || []).length === 0 && (
                          <SelectItem value="no-services" disabled>
                            No services found in this category
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  )}
                </div>
              )}

              {/* Service Details */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="serviceName">Service Name *</Label>
                  <Input
                    id="serviceName"
                    value={newService.serviceName}
                    onChange={(e) => setNewService({...newService, serviceName: e.target.value})}
                    placeholder="Enter service name"
                    disabled={!isCreatingNewService && !selectedServiceId}
                  />
                </div>

                <div>
                  <Label htmlFor="unitPrice">Unit Price *</Label>
                  <Input
                    id="unitPrice"
                    type="number"
                    min="0"
                    step="0.01"
                    value={newService.unitPrice}
                    onChange={(e) => setNewService({...newService, unitPrice: parseFloat(e.target.value) || 0})}
                    placeholder="0.00"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="serviceDescription">Description</Label>
                <Textarea
                  id="serviceDescription"
                  value={newService.serviceDescription}
                  onChange={(e) => setNewService({...newService, serviceDescription: e.target.value})}
                  placeholder="Service description (optional)"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="quantity">Quantity</Label>
                  <Input
                    id="quantity"
                    type="number"
                    min="1"
                    value={newService.quantity}
                    onChange={(e) => setNewService({...newService, quantity: parseInt(e.target.value) || 1})}
                  />
                </div>

                <div>
                  <Label htmlFor="unitPrice">Unit Price *</Label>
                  <Input
                    id="unitPrice"
                    type="number"
                    min="0"
                    step="0.01"
                    value={newService.unitPrice}
                    onChange={(e) => setNewService({...newService, unitPrice: parseFloat(e.target.value) || 0})}
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <Label htmlFor="currency">Currency</Label>
                  <Select value={newService.currency} onValueChange={(value) => setNewService({...newService, currency: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="KES">KES</SelectItem>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                      <SelectItem value="GBP">GBP</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="discountPercentage">Discount %</Label>
                  <Input
                    id="discountPercentage"
                    type="number"
                    min="0"
                    max="100"
                    value={newService.discount.percentage}
                    onChange={(e) => setNewService({
                      ...newService,
                      discount: { ...newService.discount, percentage: parseFloat(e.target.value) || 0 }
                    })}
                    placeholder="0"
                  />
                </div>

                <div>
                  <Label htmlFor="discountReason">Discount Reason</Label>
                  <Input
                    id="discountReason"
                    value={newService.discount.reason}
                    onChange={(e) => setNewService({
                      ...newService,
                      discount: { ...newService.discount, reason: e.target.value }
                    })}
                    placeholder="Reason for discount"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={newService.notes}
                  onChange={(e) => setNewService({...newService, notes: e.target.value})}
                  placeholder="Additional notes about this service"
                  rows={3}
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => {
                  setIsAddingService(false);
                  setIsCreatingNewService(false);
                  setSelectedServiceId('');
                }}>
                  Cancel
                </Button>
                <Button
                  onClick={handleSubmitService}
                  disabled={addServiceMutation.isPending || createServiceMutation.isPending}
                >
                  {isCreatingNewService
                    ? (createServiceMutation.isPending ? 'Creating...' : 'Create & Add Service')
                    : (addServiceMutation.isPending ? 'Adding...' : 'Add Service')
                  }
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Services list grouped by category */}
      {isLoading ? (
        <div className="text-center py-8">Loading services...</div>
      ) : services.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No services added to this appointment</p>
            <p className="text-sm text-gray-400 mt-2">Add services to track appointment progress</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {categories.map((category) => {
            const categoryServices = groupedServices[category];
            const categoryLabel = serviceCategories.find(c => c.value === category)?.label || category;

            return (
              <Card key={category} className="border-l-4 border-l-blue-500">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    {getCategoryIcon(category)}
                    {categoryLabel}
                    <Badge variant="secondary" className="ml-2">
                      {categoryServices.length} service{categoryServices.length !== 1 ? 's' : ''}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {categoryServices.map((service: AppointmentService) => (
                    <Card key={service.appointmentServiceId} className="hover:shadow-md transition-shadow border-l-2 border-l-gray-200">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(service.status)}
                            <h4 className="font-semibold">{service.serviceName}</h4>
                            <Badge className={getStatusColor(service.status)}>
                              {service.status}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-lg font-bold">
                              {service.currency} {service.finalAmount.toFixed(2)}
                            </span>
                            <div className="flex gap-1">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleOpenNotesModal(service)}
                              >
                                <MessageSquare className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => updateStatusMutation.mutate({
                                  serviceId: service.appointmentServiceId,
                                  status: service.status === 'pending' ? 'in_progress' :
                                         service.status === 'in_progress' ? 'completed' : 'pending'
                                })}
                              >
                                {service.status === 'pending' ? 'Start' :
                                 service.status === 'in_progress' ? 'Complete' : 'Reopen'}
                              </Button>
                            </div>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Quantity:</span>
                            <span className="ml-2 font-medium">{service.quantity}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Unit Price:</span>
                            <span className="ml-2 font-medium">{service.currency} {service.unitPrice.toFixed(2)}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Total:</span>
                            <span className="ml-2 font-medium">{service.currency} {service.totalPrice.toFixed(2)}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Performed by:</span>
                            <span className="ml-2 font-medium">{service.performedByName || 'Unknown'}</span>
                          </div>
                        </div>

                        {service.discount && (service.discount.percentage > 0 || service.discount.amount > 0) && (
                          <div className="mt-2 p-2 bg-green-50 rounded text-sm">
                            <span className="text-green-700">
                              Discount: {service.discount.percentage > 0 ? `${service.discount.percentage}%` : `${service.currency} ${service.discount.amount}`}
                              {service.discount.reason && ` (${service.discount.reason})`}
                            </span>
                          </div>
                        )}

                        {service.serviceDescription && (
                          <p className="text-gray-700 mt-2 text-sm">{service.serviceDescription}</p>
                        )}

                        {service.notes && (
                          <p className="text-gray-600 mt-2 text-sm italic">{service.notes}</p>
                        )}

                        <div className="flex justify-between items-center mt-3 text-xs text-gray-500">
                          <span>Added: {format(new Date(service.createdAt), 'MMM d, yyyy h:mm a')}</span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </CardContent>
              </Card>
            );
          })}

          {/* Totals Summary */}
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-4">
              <h4 className="font-semibold mb-3">Service Totals</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Subtotal:</span>
                  <span className="ml-2 font-medium">KES {totals.subtotal.toFixed(2)}</span>
                </div>
                <div>
                  <span className="text-gray-600">Total Discount:</span>
                  <span className="ml-2 font-medium text-green-600">-KES {totals.totalDiscount.toFixed(2)}</span>
                </div>
                <div>
                  <span className="text-gray-600">Emergency Charges:</span>
                  <span className="ml-2 font-medium">KES {totals.totalEmergencyCharges?.toFixed(2) || '0.00'}</span>
                </div>
                <div>
                  <span className="text-gray-600 font-semibold">Grand Total:</span>
                  <span className="ml-2 font-bold text-lg">KES {totals.grandTotal.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Notes Modal */}
      <Dialog open={isNotesModalOpen} onOpenChange={setIsNotesModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Service Notes
            </DialogTitle>
            <DialogDescription>
              {selectedServiceForNotes && (
                <>Add or edit notes for <strong>{selectedServiceForNotes.serviceName}</strong></>
              )}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="noteContent">Notes</Label>
              <Textarea
                id="noteContent"
                value={noteContent}
                onChange={(e) => setNoteContent(e.target.value)}
                placeholder="Enter service notes..."
                rows={6}
                className="resize-none"
              />
            </div>

            <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
              <Button
                variant="outline"
                size="sm"
                onClick={handleGenerateAINotes}
                disabled={isGeneratingAI || generateAINotesMutation.isPending}
                className="flex items-center gap-2"
              >
                <Wand2 className={`h-4 w-4 ${isGeneratingAI ? 'animate-spin' : ''}`} />
                {isGeneratingAI ? 'Generating...' : 'Generate AI Notes'}
              </Button>
              <span className="text-sm text-blue-700">
                AI can help generate professional service notes based on the service type and context.
              </span>
            </div>

            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setIsNotesModalOpen(false);
                  setSelectedServiceForNotes(null);
                  setNoteContent('');
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSaveNotes}
                disabled={updateNotesMutation.isPending}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {updateNotesMutation.isPending ? 'Saving...' : 'Save Notes'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AppointmentServices;
