import React, { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Brain,
  Lightbulb,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Pill,
  Stethoscope,
  FileText,
  ThumbsUp,
  ThumbsDown,
  Wand2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/services/api';

interface AISuggestionsProps {
  appointmentId: number;
  petData?: any;
  appointmentData?: any;
  clinicId?: number;
}

interface AISuggestion {
  suggestionId: number;
  category: string;
  suggestionType: string;
  title: string;
  description: string;
  confidenceScore: number;
  priority: string;
  medications: any[];
  treatments: any[];
  diagnosticTests: any[];
  followUp: any;
  status: string;
  reviewNotes?: string;
  createdAt: string;
}

const AISuggestions: React.FC<AISuggestionsProps> = ({
  appointmentId,
  petData,
  appointmentData,
  clinicId
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [isGenerating, setIsGenerating] = useState(false);
  const [generationContext, setGenerationContext] = useState({
    symptoms: '',
    vitalSigns: {
      temperature: '',
      heartRate: '',
      respiratoryRate: '',
      weight: '',
      bloodPressure: ''
    },
    previousConditions: '',
    currentMedications: '',
    allergies: '',
    requestedCategories: ['diagnosis', 'treatment', 'medication']
  });

  // Fetch AI suggestions
  const { data: suggestionsResponse, isLoading } = useQuery({
    queryKey: ['aiSuggestions', appointmentId],
    queryFn: async () => {
      const response = await api.get(`/appointments/${appointmentId}/ai-suggestions`);
      return response.data;
    }
  });

  const suggestions: AISuggestion[] = suggestionsResponse?.data?.suggestions || [];

  // Generate AI suggestions mutation
  const generateSuggestionsMutation = useMutation({
    mutationFn: async (context: any) => {
      const response = await api.post(`/appointments/${appointmentId}/ai-suggestions`, {
        symptoms: context.symptoms.split(',').map((s: string) => s.trim()).filter(Boolean),
        vitalSigns: context.vitalSigns,
        previousConditions: context.previousConditions.split(',').map((c: string) => c.trim()).filter(Boolean),
        currentMedications: context.currentMedications.split(',').map((m: string) => m.trim()).filter(Boolean),
        allergies: context.allergies.split(',').map((a: string) => a.trim()).filter(Boolean),
        requestedCategories: context.requestedCategories,
        clinicId,
        appointmentId
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['aiSuggestions', appointmentId] });
      setIsGenerating(false);
      toast({
        title: "Success",
        description: "AI suggestions generated successfully",
      });
    },
    onError: (error: any) => {
      setIsGenerating(false);
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to generate AI suggestions",
        variant: "destructive",
      });
    }
  });

  // Review suggestion mutation
  const reviewSuggestionMutation = useMutation({
    mutationFn: async ({ suggestionId, status, reviewNotes }: { suggestionId: number; status: string; reviewNotes: string }) => {
      const response = await api.put(`/appointments/suggestions/${suggestionId}/review`, {
        status,
        reviewNotes
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['aiSuggestions', appointmentId] });
      toast({
        title: "Success",
        description: "Suggestion reviewed successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to review suggestion",
        variant: "destructive",
      });
    }
  });

  // Implement suggestion mutation
  const implementSuggestionMutation = useMutation({
    mutationFn: async ({ suggestionId, implementationNotes }: { suggestionId: number; implementationNotes: string }) => {
      const response = await api.put(`/appointments/suggestions/${suggestionId}/implement`, {
        implementationNotes
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['aiSuggestions', appointmentId] });
      toast({
        title: "Success",
        description: "Suggestion implemented successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to implement suggestion",
        variant: "destructive",
      });
    }
  });

  const handleGenerateSuggestions = () => {
    setIsGenerating(true);
    generateSuggestionsMutation.mutate(generationContext);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accepted': return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected': return 'bg-red-100 text-red-800 border-red-200';
      case 'implemented': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'reviewed': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'diagnosis': return <Stethoscope className="h-4 w-4" />;
      case 'treatment': return <FileText className="h-4 w-4" />;
      case 'medication': return <Pill className="h-4 w-4" />;
      default: return <Lightbulb className="h-4 w-4" />;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          AI Suggestions & Recommendations
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Generate New Suggestions */}
        <Card className="bg-blue-50 border-blue-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Wand2 className="h-5 w-5" />
              Generate AI Suggestions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Symptoms (comma-separated)</Label>
                <Textarea
                  placeholder="e.g., lethargy, loss of appetite, vomiting"
                  value={generationContext.symptoms}
                  onChange={(e) => setGenerationContext(prev => ({ ...prev, symptoms: e.target.value }))}
                />
              </div>
              <div>
                <Label>Previous Conditions (comma-separated)</Label>
                <Textarea
                  placeholder="e.g., diabetes, arthritis, allergies"
                  value={generationContext.previousConditions}
                  onChange={(e) => setGenerationContext(prev => ({ ...prev, previousConditions: e.target.value }))}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              <div>
                <Label>Temperature (°F)</Label>
                <Input
                  type="number"
                  placeholder="101.5"
                  value={generationContext.vitalSigns.temperature}
                  onChange={(e) => setGenerationContext(prev => ({
                    ...prev,
                    vitalSigns: { ...prev.vitalSigns, temperature: e.target.value }
                  }))}
                />
              </div>
              <div>
                <Label>Heart Rate (bpm)</Label>
                <Input
                  type="number"
                  placeholder="120"
                  value={generationContext.vitalSigns.heartRate}
                  onChange={(e) => setGenerationContext(prev => ({
                    ...prev,
                    vitalSigns: { ...prev.vitalSigns, heartRate: e.target.value }
                  }))}
                />
              </div>
              <div>
                <Label>Weight (kg)</Label>
                <Input
                  type="number"
                  placeholder="25.5"
                  value={generationContext.vitalSigns.weight}
                  onChange={(e) => setGenerationContext(prev => ({
                    ...prev,
                    vitalSigns: { ...prev.vitalSigns, weight: e.target.value }
                  }))}
                />
              </div>
            </div>

            <Button
              onClick={handleGenerateSuggestions}
              disabled={isGenerating || generateSuggestionsMutation.isPending}
              className="w-full"
            >
              <Brain className="h-4 w-4 mr-2" />
              {isGenerating ? 'Generating AI Suggestions...' : 'Generate AI Suggestions'}
            </Button>
          </CardContent>
        </Card>

        {/* Existing Suggestions */}
        {isLoading ? (
          <div className="text-center py-8">Loading suggestions...</div>
        ) : suggestions.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No AI suggestions generated yet</p>
              <p className="text-sm text-gray-400 mt-2">Generate suggestions to get AI-powered recommendations</p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            <h3 className="font-semibold">AI Suggestions ({suggestions.length})</h3>
            {suggestions.map((suggestion) => (
              <Card key={suggestion.suggestionId} className="border-l-4 border-l-blue-500">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      {getCategoryIcon(suggestion.category)}
                      <div>
                        <h4 className="font-medium">{suggestion.title}</h4>
                        <p className="text-sm text-gray-600">{suggestion.category} • {suggestion.suggestionType}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getPriorityColor(suggestion.priority)}>
                        {suggestion.priority}
                      </Badge>
                      <Badge className={getStatusColor(suggestion.status)}>
                        {suggestion.status}
                      </Badge>
                      <Badge variant="outline">
                        {suggestion.confidenceScore}% confidence
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-700 mb-4">{suggestion.description}</p>

                  <Accordion type="single" collapsible className="w-full">
                    {suggestion.medications.length > 0 && (
                      <AccordionItem value="medications">
                        <AccordionTrigger className="text-sm">
                          <div className="flex items-center gap-2">
                            <Pill className="h-4 w-4" />
                            Medications ({suggestion.medications.length})
                          </div>
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-2">
                            {suggestion.medications.map((med, index) => (
                              <div key={index} className="p-3 bg-gray-50 rounded-lg">
                                <div className="font-medium">{med.name}</div>
                                <div className="text-sm text-gray-600">
                                  {med.dosage} • {med.frequency} • {med.duration}
                                </div>
                                {med.instructions && (
                                  <div className="text-sm text-gray-500 mt-1">{med.instructions}</div>
                                )}
                              </div>
                            ))}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    )}

                    {suggestion.treatments.length > 0 && (
                      <AccordionItem value="treatments">
                        <AccordionTrigger className="text-sm">
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            Treatments ({suggestion.treatments.length})
                          </div>
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-2">
                            {suggestion.treatments.map((treatment, index) => (
                              <div key={index} className="p-3 bg-gray-50 rounded-lg">
                                <div className="font-medium">{treatment.name}</div>
                                <div className="text-sm text-gray-600">{treatment.description}</div>
                                {treatment.estimatedCost && (
                                  <div className="text-sm text-gray-500 mt-1">
                                    Estimated cost: {treatment.currency} {treatment.estimatedCost}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    )}
                  </Accordion>

                  {/* Action Buttons */}
                  {suggestion.status === 'pending' && (
                    <div className="flex gap-2 mt-4">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => reviewSuggestionMutation.mutate({
                          suggestionId: suggestion.suggestionId,
                          status: 'accepted',
                          reviewNotes: 'Approved for implementation'
                        })}
                        className="flex items-center gap-1"
                      >
                        <ThumbsUp className="h-3 w-3" />
                        Accept
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => reviewSuggestionMutation.mutate({
                          suggestionId: suggestion.suggestionId,
                          status: 'rejected',
                          reviewNotes: 'Not suitable for this case'
                        })}
                        className="flex items-center gap-1"
                      >
                        <ThumbsDown className="h-3 w-3" />
                        Reject
                      </Button>
                    </div>
                  )}

                  {suggestion.status === 'accepted' && (
                    <Button
                      size="sm"
                      onClick={() => implementSuggestionMutation.mutate({
                        suggestionId: suggestion.suggestionId,
                        implementationNotes: 'Implemented as suggested'
                      })}
                      className="mt-4"
                    >
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Implement
                    </Button>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AISuggestions;
