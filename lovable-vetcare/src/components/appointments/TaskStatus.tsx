import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  CheckCircle,
  Clock,
  User,
  AlertCircle,
  FileText,
  Users,
  Timer,
  Calendar,
  Signature,
  BarChart3
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/services/api';

interface TaskStatusProps {
  appointmentId: number;
  clinicId?: number;
}

interface CategoryTask {
  category: string;
  assignedStaff: number;
  assignedStaffName?: string;
  isCompleted: boolean;
  completedAt?: string;
  completedBy?: number;
  completedByName?: string;
  signature?: string;
  estimatedDuration: number;
  actualDuration?: number;
  priority: string;
}

interface TaskSummary {
  totalTasks: number;
  completedTasks: number;
  pendingTasks: number;
  completionPercentage: number;
  totalEstimatedDuration: number;
  totalActualDuration: number;
  isFullyCompleted: boolean;
}

const TaskStatus: React.FC<TaskStatusProps> = ({ appointmentId, clinicId }) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [selectedTask, setSelectedTask] = useState<CategoryTask | null>(null);
  const [showCompleteModal, setShowCompleteModal] = useState(false);
  const [completionData, setCompletionData] = useState({
    signature: '',
    actualDuration: 0,
    notes: ''
  });

  // Fetch task status
  const { data: taskStatusResponse, isLoading } = useQuery({
    queryKey: ['appointmentTaskStatus', appointmentId],
    queryFn: async () => {
      const response = await api.get(`/appointments/${appointmentId}/task-status`);
      return response.data;
    }
  });

  const appointment = taskStatusResponse?.data?.appointment;
  const taskSummary: TaskSummary = taskStatusResponse?.data?.taskSummary || {
    totalTasks: 0,
    completedTasks: 0,
    pendingTasks: 0,
    completionPercentage: 0,
    totalEstimatedDuration: 0,
    totalActualDuration: 0,
    isFullyCompleted: false
  };

  const selectedCategories: CategoryTask[] = appointment?.selectedCategories || [];

  // Complete task mutation
  const completeTaskMutation = useMutation({
    mutationFn: async ({ category, data }: { category: string; data: any }) => {
      const response = await api.post(`/appointments/${appointmentId}/categories/${category}/complete`, {
        ...data,
        clinicId,
        appointmentId
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointmentTaskStatus', appointmentId] });
      setShowCompleteModal(false);
      setSelectedTask(null);
      setCompletionData({ signature: '', actualDuration: 0, notes: '' });
      toast({
        title: "Success",
        description: "Task completed successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to complete task",
        variant: "destructive",
      });
    }
  });

  // Generate summary mutation
  const generateSummaryMutation = useMutation({
    mutationFn: async () => {
      const response = await api.post(`/appointments/${appointmentId}/generate-summary`, {
        clinicId,
        appointmentId
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointmentTaskStatus', appointmentId] });
      toast({
        title: "Success",
        description: "Appointment summary generated successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to generate summary",
        variant: "destructive",
      });
    }
  });

  const handleCompleteTask = (task: CategoryTask) => {
    setSelectedTask(task);
    setCompletionData({
      signature: '',
      actualDuration: task.estimatedDuration,
      notes: ''
    });
    setShowCompleteModal(true);
  };

  const handleSubmitCompletion = () => {
    if (!selectedTask) return;

    completeTaskMutation.mutate({
      category: selectedTask.category,
      data: completionData
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'normal': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'consultation': return <FileText className="h-4 w-4" />;
      case 'surgery': return <AlertCircle className="h-4 w-4" />;
      case 'laboratory': return <BarChart3 className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-gray-600">Loading task status...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Task Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Task Progress Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{taskSummary.totalTasks}</div>
              <div className="text-sm text-gray-600">Total Tasks</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{taskSummary.completedTasks}</div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{taskSummary.pendingTasks}</div>
              <div className="text-sm text-gray-600">Pending</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{taskSummary.totalActualDuration}min</div>
              <div className="text-sm text-gray-600">Actual Duration</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-indigo-600">{taskSummary.totalEstimatedDuration}min</div>
              <div className="text-sm text-gray-600">Estimated Duration</div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Overall Progress</span>
                <span>{taskSummary.completionPercentage}%</span>
              </div>
              <Progress value={taskSummary.completionPercentage} className="h-3" />
            </div>

            {/* Individual Task Progress */}
            {selectedCategories.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium text-sm">Individual Task Progress</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {selectedCategories.map((task) => (
                    <div key={task.category} className="p-3 border rounded-lg bg-gray-50">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium capitalize">{task.category}</span>
                        <Badge className={task.isCompleted ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'}>
                          {task.isCompleted ? 'Done' : 'Pending'}
                        </Badge>
                      </div>
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs text-gray-600">
                          <span>Progress</span>
                          <span>{task.isCompleted ? '100%' : '0%'}</span>
                        </div>
                        <Progress value={task.isCompleted ? 100 : 0} className="h-1" />
                        <div className="text-xs text-gray-500">
                          {task.actualDuration ? `${task.actualDuration}min` : `Est: ${task.estimatedDuration}min`}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {taskSummary.isFullyCompleted && (
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2 text-green-800">
                <CheckCircle className="h-4 w-4" />
                <span className="font-medium">All tasks completed!</span>
              </div>
              <Button
                onClick={() => generateSummaryMutation.mutate()}
                disabled={generateSummaryMutation.isPending}
                className="mt-2 bg-green-600 hover:bg-green-700"
                size="sm"
              >
                {generateSummaryMutation.isPending ? 'Generating...' : 'Generate Final Summary'}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Individual Tasks */}
      <Card>
        <CardHeader>
          <CardTitle>Task Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {selectedCategories.map((task) => (
              <Card key={task.category} className={`border-l-4 ${task.isCompleted ? 'border-l-green-500' : 'border-l-orange-500'}`}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      {getCategoryIcon(task.category)}
                      <div>
                        <h4 className="font-medium capitalize">{task.category}</h4>
                        <p className="text-sm text-gray-600">
                          Assigned to: {task.assignedStaffName || `Staff ID ${task.assignedStaff}`}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getPriorityColor(task.priority)}>
                        {task.priority}
                      </Badge>
                      {task.isCompleted ? (
                        <Badge className="bg-green-100 text-green-800 border-green-200">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Completed
                        </Badge>
                      ) : (
                        <Badge className="bg-orange-100 text-orange-800 border-orange-200">
                          <Clock className="h-3 w-3 mr-1" />
                          Pending
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Estimated:</span>
                      <div className="font-medium">{task.estimatedDuration} min</div>
                    </div>
                    {task.actualDuration && (
                      <div>
                        <span className="text-gray-500">Actual:</span>
                        <div className="font-medium">{task.actualDuration} min</div>
                      </div>
                    )}
                    {task.completedAt && (
                      <div>
                        <span className="text-gray-500">Completed:</span>
                        <div className="font-medium">{new Date(task.completedAt).toLocaleString()}</div>
                      </div>
                    )}
                    {task.signature && (
                      <div>
                        <span className="text-gray-500">Signature:</span>
                        <div className="font-medium flex items-center gap-1">
                          <Signature className="h-3 w-3" />
                          Signed
                        </div>
                      </div>
                    )}
                  </div>

                  {!task.isCompleted && (
                    <div className="mt-3">
                      <Button
                        onClick={() => handleCompleteTask(task)}
                        size="sm"
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Complete Task
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Complete Task Modal */}
      <Dialog open={showCompleteModal} onOpenChange={setShowCompleteModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Complete Task: {selectedTask?.category}</DialogTitle>
            <DialogDescription>
              Mark this task as completed and provide completion details.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label>Digital Signature</Label>
              <Input
                value={completionData.signature}
                onChange={(e) => setCompletionData(prev => ({ ...prev, signature: e.target.value }))}
                placeholder="Enter your name or signature"
              />
            </div>
            <div>
              <Label>Actual Duration (minutes)</Label>
              <Input
                type="number"
                value={completionData.actualDuration}
                onChange={(e) => setCompletionData(prev => ({ ...prev, actualDuration: parseInt(e.target.value) }))}
                min="1"
              />
            </div>
            <div>
              <Label>Completion Notes (Optional)</Label>
              <Textarea
                value={completionData.notes}
                onChange={(e) => setCompletionData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Any additional notes about task completion..."
                rows={3}
              />
            </div>
            <Button
              onClick={handleSubmitCompletion}
              disabled={completeTaskMutation.isPending || !completionData.signature}
              className="w-full"
            >
              {completeTaskMutation.isPending ? 'Completing...' : 'Complete Task'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TaskStatus;
