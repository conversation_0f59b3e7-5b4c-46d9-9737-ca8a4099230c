import React, { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  User,
  Stethoscope,
  Calendar,
  Timer
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/services/api';
import { getAllStaff } from '@/services/staff';

interface TaskAssignmentProps {
  appointmentId: number;
  selectedAppointmentTypes: any[];
  clinicId?: number;
  onAssignmentComplete?: () => void;
}

interface CategoryAssignment {
  category: string;
  assignedStaff: number;
  estimatedDuration: number;
  priority: 'low' | 'normal' | 'high' | 'urgent';
}

const serviceCategories = [
  { value: 'consultation', label: 'Consultation', icon: Stethoscope, defaultDuration: 30 },
  { value: 'vaccination', label: 'Vaccination', icon: Users, defaultDuration: 15 },
  { value: 'laboratory', label: 'Laboratory', icon: AlertCircle, defaultDuration: 45 },
  { value: 'surgery', label: 'Surgery', icon: Users, defaultDuration: 120 },
  { value: 'grooming', label: 'Grooming', icon: Users, defaultDuration: 60 },
  { value: 'dental', label: 'Dental', icon: Users, defaultDuration: 90 },
  { value: 'emergency', label: 'Emergency', icon: AlertCircle, defaultDuration: 60 },
  { value: 'medication', label: 'Medication', icon: Users, defaultDuration: 20 },
  { value: 'therapy', label: 'Therapy', icon: Users, defaultDuration: 45 },
  { value: 'imaging', label: 'Imaging', icon: Users, defaultDuration: 30 },
  { value: 'other', label: 'Other', icon: Users, defaultDuration: 30 }
];

const TaskAssignment: React.FC<TaskAssignmentProps> = ({
  appointmentId,
  selectedAppointmentTypes,
  clinicId,
  onAssignmentComplete
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [categoryAssignments, setCategoryAssignments] = useState<CategoryAssignment[]>([]);
  const [vetInCharge, setVetInCharge] = useState<string>('');

  // Map appointment types to categories
  const appointmentTypeToCategory: { [key: string]: string } = {
    'Vaccination': 'vaccination',
    'Consultation': 'consultation',
    'Laboratory': 'laboratory',
    'Surgery': 'surgery',
    'Grooming': 'grooming',
    'Dental': 'dental',
    'Emergency': 'emergency',
    'Wellness Check': 'wellness',
    'Physical Therapy': 'therapy',
    'X-ray/Imaging': 'imaging',
    'Behavioral Consultation': 'behavioral',
    'Nutrition Consultation': 'nutrition',
    'Medication': 'medication',
    'Follow-up': 'follow_up',
    'Other': 'other'
  };

  // Preselect categories based on appointment types
  useEffect(() => {
    if (selectedAppointmentTypes && selectedAppointmentTypes.length > 0) {
      const preselectedCategories: string[] = [];
      const preselectedAssignments: CategoryAssignment[] = [];

      selectedAppointmentTypes.forEach((appointmentType: any) => {
        const typeName = appointmentType.name || appointmentType.appointmentTypeName;
        const category = appointmentTypeToCategory[typeName] || 'consultation';

        if (!preselectedCategories.includes(category)) {
          preselectedCategories.push(category);

          const categoryInfo = serviceCategories.find(c => c.value === category);
          preselectedAssignments.push({
            category,
            assignedStaff: 0,
            estimatedDuration: categoryInfo?.defaultDuration || 30,
            priority: 'normal'
          });
        }
      });

      setSelectedCategories(preselectedCategories);
      setCategoryAssignments(preselectedAssignments);
    }
  }, [selectedAppointmentTypes]);

  // Fetch staff data
  const { data: staffResponse } = useQuery({
    queryKey: ['staff'],
    queryFn: getAllStaff
  });

  const staff = staffResponse?.data?.data || [];
  const veterinarians = staff.filter((s: any) =>
    s.jobTitle?.toLowerCase().includes('vet') ||
    s.jobTitle?.toLowerCase().includes('doctor')
  );

  // Assign categories mutation
  const assignCategoriesMutation = useMutation({
    mutationFn: async (data: { selectedCategories: CategoryAssignment[]; vetInCharge: number }) => {
      const response = await api.post(`/appointments/${appointmentId}/assign-categories`, {
        ...data,
        clinicId,
        appointmentId
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointment', appointmentId] });
      toast({
        title: "Success",
        description: "Categories assigned successfully",
      });
      if (onAssignmentComplete) {
        onAssignmentComplete();
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to assign categories",
        variant: "destructive",
      });
    }
  });

  const handleCategoryToggle = (category: string) => {
    if (selectedCategories.includes(category)) {
      setSelectedCategories(prev => prev.filter(c => c !== category));
      setCategoryAssignments(prev => prev.filter(a => a.category !== category));
    } else {
      setSelectedCategories(prev => [...prev, category]);
      const categoryInfo = serviceCategories.find(c => c.value === category);
      setCategoryAssignments(prev => [...prev, {
        category,
        assignedStaff: 0,
        estimatedDuration: categoryInfo?.defaultDuration || 30,
        priority: 'normal'
      }]);
    }
  };

  const updateAssignment = (category: string, field: keyof CategoryAssignment, value: any) => {
    setCategoryAssignments(prev => prev.map(assignment =>
      assignment.category === category
        ? { ...assignment, [field]: value }
        : assignment
    ));
  };

  const handleAssignCategories = () => {
    if (categoryAssignments.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one category",
        variant: "destructive",
      });
      return;
    }

    if (!vetInCharge) {
      toast({
        title: "Error",
        description: "Please select a veterinarian in charge",
        variant: "destructive",
      });
      return;
    }

    // Validate all assignments have staff
    const unassignedCategories = categoryAssignments.filter(a => !a.assignedStaff);
    if (unassignedCategories.length > 0) {
      toast({
        title: "Error",
        description: "Please assign staff to all selected categories",
        variant: "destructive",
      });
      return;
    }

    assignCategoriesMutation.mutate({
      selectedCategories: categoryAssignments,
      vetInCharge: parseInt(vetInCharge)
    });
  };

  const getCategoryIcon = (category: string) => {
    const categoryInfo = serviceCategories.find(c => c.value === category);
    const IconComponent = categoryInfo?.icon || Users;
    return <IconComponent className="h-4 w-4" />;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'normal': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Task Assignment & Category Management
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Veterinarian in Charge Selection */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <Stethoscope className="h-4 w-4" />
            Veterinarian in Charge
          </Label>
          <Select value={vetInCharge} onValueChange={setVetInCharge}>
            <SelectTrigger>
              <SelectValue placeholder="Select veterinarian in charge" />
            </SelectTrigger>
            <SelectContent>
              {veterinarians.map((vet: any) => (
                <SelectItem key={vet.staffId} value={vet.staffId.toString()}>
                  Dr. {vet.firstName} {vet.lastName} - {vet.jobTitle}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Category Selection */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label>Select Categories for this Appointment</Label>
            {selectedAppointmentTypes.length > 0 && (
              <div className="text-sm text-blue-600">
                {selectedCategories.length} categories preselected from appointment types
              </div>
            )}
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {serviceCategories.map((category) => {
              const isPreselected = selectedAppointmentTypes.some((appointmentType: any) => {
                const typeName = appointmentType.name || appointmentType.appointmentTypeName;
                return appointmentTypeToCategory[typeName] === category.value;
              });

              return (
                <div
                  key={category.value}
                  className={`p-3 border rounded-lg cursor-pointer transition-all ${
                    selectedCategories.includes(category.value)
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  } ${isPreselected ? 'ring-2 ring-green-200' : ''}`}
                  onClick={() => handleCategoryToggle(category.value)}
                >
                  <div className="flex items-center gap-2">
                    {getCategoryIcon(category.value)}
                    <span className="text-sm font-medium">{category.label}</span>
                    {isPreselected && (
                      <span className="text-xs bg-green-100 text-green-700 px-1 rounded">Auto</span>
                    )}
                    {selectedCategories.includes(category.value) && (
                      <CheckCircle className="h-4 w-4 text-blue-500 ml-auto" />
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Category Assignments */}
        {categoryAssignments.length > 0 && (
          <div className="space-y-4">
            <Label>Assign Staff to Categories</Label>
            {categoryAssignments.map((assignment) => {
              const categoryInfo = serviceCategories.find(c => c.value === assignment.category);
              return (
                <Card key={assignment.category} className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    {getCategoryIcon(assignment.category)}
                    <span className="font-medium">{categoryInfo?.label}</span>
                    <Badge className={getPriorityColor(assignment.priority)}>
                      {assignment.priority}
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <div>
                      <Label className="text-xs">Assigned Staff</Label>
                      <Select
                        value={assignment.assignedStaff.toString()}
                        onValueChange={(value) => updateAssignment(assignment.category, 'assignedStaff', parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select staff" />
                        </SelectTrigger>
                        <SelectContent>
                          {staff.map((member: any) => (
                            <SelectItem key={member.staffId} value={member.staffId.toString()}>
                              {member.firstName} {member.lastName} - {member.jobTitle}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-xs flex items-center gap-1">
                        <Timer className="h-3 w-3" />
                        Duration (minutes)
                      </Label>
                      <Input
                        type="number"
                        value={assignment.estimatedDuration}
                        onChange={(e) => updateAssignment(assignment.category, 'estimatedDuration', parseInt(e.target.value))}
                        min="5"
                        max="300"
                      />
                    </div>

                    <div>
                      <Label className="text-xs">Priority</Label>
                      <Select
                        value={assignment.priority}
                        onValueChange={(value) => updateAssignment(assignment.category, 'priority', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">Low</SelectItem>
                          <SelectItem value="normal">Normal</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                          <SelectItem value="urgent">Urgent</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>
        )}

        {/* Assignment Summary */}
        {categoryAssignments.length > 0 && (
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-blue-800">Assignment Summary</span>
              </div>
              <div className="text-sm text-blue-700">
                <p>Categories: {categoryAssignments.length}</p>
                <p>Total Estimated Duration: {categoryAssignments.reduce((sum, a) => sum + a.estimatedDuration, 0)} minutes</p>
                <p>Staff Members Involved: {new Set(categoryAssignments.map(a => a.assignedStaff)).size}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4">
          <Button
            onClick={handleAssignCategories}
            disabled={assignCategoriesMutation.isPending || categoryAssignments.length === 0}
            className="flex-1"
          >
            {assignCategoriesMutation.isPending ? 'Assigning...' : 'Assign Categories & Start Tasks'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default TaskAssignment;
