import { api } from './api';

export interface Service {
  serviceId: number;
  serviceName: string;
  serviceCode?: string;
  description: string;
  category: string;
  defaultPrice: number;
  currency: string;
  estimatedDuration: number;
  requiresAnesthesia?: boolean;
  requiresSpecialist?: boolean;
  specialistType?: string;
  prerequisites?: string[];
  contraindications?: string[];
  followUpRequired?: boolean;
  riskLevel?: 'low' | 'medium' | 'high';
  speciesRestrictions?: string[];
  breedRestrictions?: string[];
  isActive: boolean;
  isCustom: boolean;
  clinicId?: number;
  appointmentId?: number;
  createdBy?: number;
  tags?: string[];
  equipmentRequired?: string[];
  materialsRequired?: string[];
  billing?: {
    isInsuranceCovered?: boolean;
    taxable?: boolean;
    taxRate?: number;
  };
  createdAt?: string;
  updatedAt?: string;
}

export interface ServiceResponse {
  success: boolean;
  status: number;
  message: string;
  data: Service | null;
}

export interface ServicesResponse {
  success: boolean;
  status: number;
  message: string;
  data: {
    services: Service[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

export interface ServiceSearchParams {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  isActive?: boolean;
  isCustom?: boolean;
  clinicId?: number;
  appointmentId?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  [key: string]: any;
}

/**
 * Get all services with optional filtering
 */
export const getServices = async (params: ServiceSearchParams = {}): Promise<ServicesResponse> => {
  try {
    const queryParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, String(value));
      }
    });

    return await api.get<ServicesResponse>(`/services?${queryParams.toString()}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch services',
      data: {
        services: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          pages: 0
        }
      }
    };
  }
};

/**
 * Get services by category
 */
export const getServicesByCategory = async (category: string, params: ServiceSearchParams = {}): Promise<ServicesResponse> => {
  return getServices({ ...params, category });
};

/**
 * Get a single service by ID
 */
export const getServiceById = async (serviceId: string): Promise<ServiceResponse> => {
  try {
    return await api.get<ServiceResponse>(`/services/${serviceId}`);
  } catch (error: any) {
    return {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to fetch service',
      data: null
    };
  }
};

/**
 * Create a new service
 */
export const createService = async (serviceData: Partial<Service>): Promise<ServiceResponse> => {
  try {
    return await api.post<ServiceResponse>('/services', serviceData);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to create service',
      data: null
    };
  }
};

/**
 * Update an existing service
 */
export const updateService = async (
  serviceId: string,
  serviceData: Partial<Omit<Service, 'serviceId'>>
): Promise<ServiceResponse> => {
  try {
    return await api.put<ServiceResponse>(`/services/${serviceId}`, serviceData);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to update service',
      data: null
    };
  }
};

/**
 * Delete a service
 */
export const deleteService = async (serviceId: string): Promise<ServiceResponse> => {
  try {
    return await api.delete<ServiceResponse>(`/services/${serviceId}`);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to delete service',
      data: null
    };
  }
};

/**
 * Toggle service active status
 */
export const toggleServiceStatus = async (serviceId: string): Promise<ServiceResponse> => {
  try {
    return await api.patch<ServiceResponse>(`/services/${serviceId}/toggle-status`);
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to toggle service status',
      data: null
    };
  }
};

/**
 * Get services for a specific clinic
 */
export const getClinicServices = async (clinicId: string, params: ServiceSearchParams = {}): Promise<ServicesResponse> => {
  return getServices({ ...params, clinicId: parseInt(clinicId) });
};

/**
 * Search services by name or description
 */
export const searchServices = async (query: string, params: ServiceSearchParams = {}): Promise<ServicesResponse> => {
  return getServices({ ...params, search: query });
};

/**
 * Get default services (system-wide services)
 */
export const getDefaultServices = async (params: ServiceSearchParams = {}): Promise<ServicesResponse> => {
  return getServices({ ...params, isCustom: false });
};

/**
 * Get services for a specific appointment
 */
export const getAppointmentServices = async (appointmentId: number, params: ServiceSearchParams = {}): Promise<ServicesResponse> => {
  return getServices({ ...params, appointmentId });
};

/**
 * Get custom services (clinic-specific services)
 */
export const getCustomServices = async (params: ServiceSearchParams = {}): Promise<ServicesResponse> => {
  return getServices({ ...params, isCustom: true });
};

/**
 * Get active services only
 */
export const getActiveServices = async (params: ServiceSearchParams = {}): Promise<ServicesResponse> => {
  return getServices({ ...params, isActive: true });
};

/**
 * Bulk update services
 */
export const bulkUpdateServices = async (updates: Array<{ serviceId: string; data: Partial<Service> }>): Promise<ServiceResponse> => {
  try {
    return await api.put<ServiceResponse>('/services/bulk-update', { updates });
  } catch (error: any) {
    throw {
      success: false,
      status: error.status || 500,
      message: error.message || 'Failed to bulk update services',
      data: null
    };
  }
};

/**
 * Get service categories
 */
export const getServiceCategories = async (): Promise<{ success: boolean; data: string[] }> => {
  try {
    const response = await api.get<{ success: boolean; data: string[] }>('/services/categories');
    return response;
  } catch (error: any) {
    return {
      success: false,
      data: [
        'consultation', 'vaccination', 'laboratory', 'surgery', 'grooming',
        'dental', 'emergency', 'medication', 'therapy', 'imaging', 'other'
      ]
    };
  }
};
