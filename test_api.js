const http = require('http');

// Test data
const serviceData = {
  serviceName: 'Test Service with AppointmentId',
  description: 'Test service',
  defaultPrice: 100,
  estimatedDuration: 30,
  category: 'consultation',
  currency: 'KES',
  appointmentId: 1234
};

const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODMxM2Q3Nzcw' +
              'YzIwMjFlOGFmZjBmNzMiLCJpYXQiOjE3NDgxNzcwMjksImV4cCI6MTc0ODI2MzQyOX0.' +
              'rwLk7PHmaLRf2AKMd5wBUkZRbpbY8kvMNWQCrcXm1j0';

const data = JSON.stringify(serviceData);

// Test service creation
console.log('Testing service creation with appointmentId...');
const options = {
  hostname: 'localhost',
  port: 5500,
  path: '/api/v1/services',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
    'Content-Length': data.length
  }
};

const req = http.request(options, (res) => {
  console.log('Status:', res.statusCode);
  
  let body = '';
  res.on('data', (chunk) => {
    body += chunk;
  });
  
  res.on('end', () => {
    console.log('Response:', body);
    
    // Test fetching services with appointmentId filter
    testFetchWithAppointmentId();
  });
});

req.on('error', (e) => {
  console.error('Error:', e.message);
});

req.write(data);
req.end();

function testFetchWithAppointmentId() {
  console.log('\nTesting service fetch with appointmentId filter...');
  
  const fetchOptions = {
    hostname: 'localhost',
    port: 5500,
    path: '/api/v1/services?appointmentId=1234&limit=10',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  };

  const fetchReq = http.request(fetchOptions, (res) => {
    console.log('Fetch Status:', res.statusCode);
    
    let body = '';
    res.on('data', (chunk) => {
      body += chunk;
    });
    
    res.on('end', () => {
      console.log('Fetch Response:', body);
    });
  });

  fetchReq.on('error', (e) => {
    console.error('Fetch Error:', e.message);
  });

  fetchReq.end();
}
