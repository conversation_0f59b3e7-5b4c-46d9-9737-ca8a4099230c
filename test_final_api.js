const http = require('http');

const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODMxM2Q3NzcwYzIwMjFlOGFmZjBmNzMiLCJpYXQiOjE3NDgxNzgyNjMsImV4cCI6MTc0ODI2NDY2M30.dBYuW6ng42h3G_9hWLMrwnXTJkoPhq3GxT05-qBD020';

// Test data with appointmentId
const serviceData = {
  serviceName: 'Final API Test Service v2',
  description: 'Testing appointmentId via API with fix',
  defaultPrice: 450,
  estimatedDuration: 50,
  category: 'consultation',
  currency: 'KES',
  appointmentId: 7777,
  isActive: true,
  isCustom: true
};

const data = JSON.stringify(serviceData);

console.log('Testing service creation via API...');
console.log('Sending data:', JSON.stringify(serviceData, null, 2));

const options = {
  hostname: 'localhost',
  port: 5500,
  path: '/api/v1/services',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
    'Content-Length': data.length
  }
};

const req = http.request(options, (res) => {
  console.log('Status:', res.statusCode);

  let body = '';
  res.on('data', (chunk) => {
    body += chunk;
  });

  res.on('end', () => {
    console.log('Response:', body);

    try {
      const response = JSON.parse(body);
      if (response.success && response.data) {
        const serviceId = response.data.serviceId;
        console.log('\n✅ Service created with ID:', serviceId);
        console.log('✅ appointmentId in response:', response.data.appointmentId);

        // Test fetching services with appointmentId filter
        setTimeout(() => testFetchWithAppointmentId(serviceId), 1000);
      }
    } catch (e) {
      console.error('Error parsing response:', e.message);
    }
  });
});

req.on('error', (e) => {
  console.error('Error:', e.message);
});

req.write(data);
req.end();

function testFetchWithAppointmentId(serviceId) {
  console.log('\n🔍 Testing service fetch with appointmentId filter...');

  const fetchOptions = {
    hostname: 'localhost',
    port: 5500,
    path: '/api/v1/services?appointmentId=8888&limit=10',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  };

  const fetchReq = http.request(fetchOptions, (res) => {
    console.log('Fetch Status:', res.statusCode);

    let body = '';
    res.on('data', (chunk) => {
      body += chunk;
    });

    res.on('end', () => {
      console.log('Fetch Response:', body);

      try {
        const response = JSON.parse(body);
        if (response.success && response.data) {
          console.log('\n✅ Filtered services count:', response.data.services.length);
          if (response.data.services.length > 0) {
            console.log('✅ First service appointmentId:', response.data.services[0].appointmentId);
            console.log('✅ appointmentId filtering works!');
          } else {
            console.log('❌ No services found with appointmentId filter');
          }
        }
      } catch (e) {
        console.error('Error parsing fetch response:', e.message);
      }

      // Test fetching by service ID
      setTimeout(() => testFetchById(serviceId), 1000);
    });
  });

  fetchReq.on('error', (e) => {
    console.error('Fetch Error:', e.message);
  });

  fetchReq.end();
}

function testFetchById(serviceId) {
  console.log('\n🔍 Testing service fetch by ID...');

  const fetchOptions = {
    hostname: 'localhost',
    port: 5500,
    path: `/api/v1/services/${serviceId}`,
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  };

  const fetchReq = http.request(fetchOptions, (res) => {
    console.log('Fetch by ID Status:', res.statusCode);

    let body = '';
    res.on('data', (chunk) => {
      body += chunk;
    });

    res.on('end', () => {
      console.log('Fetch by ID Response:', body);

      try {
        const response = JSON.parse(body);
        if (response.success && response.data) {
          console.log('\n✅ Service fetched by ID');
          console.log('✅ appointmentId in fetched service:', response.data.appointmentId);

          if (response.data.appointmentId === 8888) {
            console.log('\n🎉 SUCCESS! appointmentId is working correctly in the API!');
          } else {
            console.log('\n❌ appointmentId mismatch in fetched service');
          }
        }
      } catch (e) {
        console.error('Error parsing fetch by ID response:', e.message);
      }
    });
  });

  fetchReq.on('error', (e) => {
    console.error('Fetch by ID Error:', e.message);
  });

  fetchReq.end();
}
